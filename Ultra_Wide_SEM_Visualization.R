# ===============================================================================
# Ultra-Wide SEM Visualization - Maximum Compatibility
# Uses extremely wide format to ensure complete visibility
# ===============================================================================

library(DiagrammeR)

# ===============================================================================
# Ultra-Wide SEM Diagram
# ===============================================================================

create_ultra_wide_sem_diagram <- function() {
  
  diagram <- grViz("
  digraph ultra_wide_sem {
    
    # Graph settings - ultra-wide format
    graph [layout = dot, 
           rankdir = LR, 
           bgcolor = white, 
           fontname = 'Arial',
           fontsize = 11,
           margin = 1.0,
           pad = 1.0,
           ranksep = 2.0,
           nodesep = 1.5,
           size = '20,8!',
           ratio = fill]
    
    # Node styling - medium size for clarity
    node [shape = box, 
          style = 'filled,rounded', 
          fontname = 'Arial', 
          fontsize = 10,
          width = 1.6,
          height = 0.9,
          margin = 0.1]
    
    # Edge styling
    edge [fontname = 'Arial', 
          fontsize = 9,
          labeldistance = 2.0,
          minlen = 2]
    
    # ===== STRICT RANK STRUCTURE =====
    subgraph cluster_0 {
      rank = same
      DC RC
      style = invis
    }
    
    subgraph cluster_1 {
      rank = same
      SIC
      style = invis
    }
    
    subgraph cluster_2 {
      rank = same
      TBL_E TBL_S TBL_EN
      style = invis
    }
    
    subgraph cluster_3 {
      rank = same
      CA
      style = invis
    }
    
    # Moderators positioned separately
    ED [pos = '1,6!']
    MCI [pos = '15,2!']
    
    # ===== NODE DEFINITIONS =====
    
    # Independent Variables
    DC [label = 'Dynamic\\nCapabilities\\n(DC)', 
        fillcolor = '#E3F2FD', color = '#1976D2']
    
    RC [label = 'Relational\\nCoordination\\n(RC)', 
        fillcolor = '#E8F5E8', color = '#388E3C']
    
    # Mediator Variable
    SIC [label = 'Service Innovation\\nCapability\\n(SIC)', 
         fillcolor = '#FFF3E0', color = '#F57C00']
    
    # TBL Performance Dimensions
    TBL_E [label = 'Economic\\nPerformance\\n(TBL-E)', 
           fillcolor = '#E8F5E8', color = '#4CAF50']
    
    TBL_S [label = 'Social\\nPerformance\\n(TBL-S)', 
           fillcolor = '#E8F5E8', color = '#4CAF50']
    
    TBL_EN [label = 'Environmental\\nPerformance\\n(TBL-EN)', 
            fillcolor = '#E8F5E8', color = '#4CAF50']
    
    # Dependent Variable
    CA [label = 'Competitive\\nAdvantage\\n(CA)', 
        fillcolor = '#FCE4EC', color = '#C2185B']
    
    # Moderator Variables
    ED [label = 'Environmental\\nDynamism\\n(ED)', 
        fillcolor = '#F3E5F5', color = '#7B1FA2', shape = ellipse]
    
    MCI [label = 'Market Competition\\nIntensity\\n(MCI)', 
         fillcolor = '#F3E5F5', color = '#7B1FA2', shape = ellipse]
    
    # ===== MAIN STRUCTURAL PATHS =====
    
    # Sequential flow: DC/RC → SIC → TBL → CA
    DC -> SIC [label = 'H1', color = '#1976D2', penwidth = 3, fontcolor = '#1976D2']
    RC -> SIC [label = 'H2', color = '#388E3C', penwidth = 3, fontcolor = '#388E3C']
    
    SIC -> TBL_E [label = 'H3a', color = '#F57C00', penwidth = 3, fontcolor = '#F57C00']
    SIC -> TBL_S [label = 'H3b', color = '#F57C00', penwidth = 3, fontcolor = '#F57C00']
    SIC -> TBL_EN [label = 'H3c', color = '#F57C00', penwidth = 3, fontcolor = '#F57C00']
    
    TBL_E -> CA [label = 'H4a', color = '#4CAF50', penwidth = 3, fontcolor = '#4CAF50']
    TBL_S -> CA [label = 'H4b', color = '#4CAF50', penwidth = 3, fontcolor = '#4CAF50']
    TBL_EN -> CA [label = 'H4c', color = '#4CAF50', penwidth = 3, fontcolor = '#4CAF50']
    
    # ===== DIRECT EFFECTS (H5) =====
    DC -> TBL_E [label = 'H5a', color = '#FF5722', style = dashed, penwidth = 2, fontcolor = '#FF5722']
    DC -> TBL_S [label = 'H5a', color = '#FF5722', style = dashed, penwidth = 2, fontcolor = '#FF5722']
    DC -> TBL_EN [label = 'H5a', color = '#FF5722', style = dashed, penwidth = 2, fontcolor = '#FF5722']
    
    RC -> TBL_E [label = 'H5b', color = '#FF5722', style = dashed, penwidth = 2, fontcolor = '#FF5722']
    RC -> TBL_S [label = 'H5b', color = '#FF5722', style = dashed, penwidth = 2, fontcolor = '#FF5722']
    RC -> TBL_EN [label = 'H5b', color = '#FF5722', style = dashed, penwidth = 2, fontcolor = '#FF5722']
    
    # ===== MODERATION EFFECTS (H7) =====
    ED -> SIC [label = 'H7\\nModerates\\nDC/RC→SIC', color = '#7B1FA2', style = dotted, penwidth = 2, fontcolor = '#7B1FA2']
    MCI -> CA [label = 'H7\\nModerates\\nTBL→CA', color = '#7B1FA2', style = dotted, penwidth = 2, fontcolor = '#7B1FA2']
    
    # ===== COVARIANCES =====
    DC -> RC [dir = both, color = '#9E9E9E', style = dotted, penwidth = 1]
    TBL_E -> TBL_S [dir = both, color = '#9E9E9E', style = dotted, penwidth = 0.5]
    TBL_S -> TBL_EN [dir = both, color = '#9E9E9E', style = dotted, penwidth = 0.5]
    
    # Title
    labelloc = 't'
    label = 'Structural Equation Model: Dynamic Capabilities and Triple Bottom Line Performance'
  }
  ")
  
  return(diagram)
}

# ===============================================================================
# Generate Ultra-Wide Diagram
# ===============================================================================

generate_ultra_wide_diagram <- function() {
  
  cat("Generating ultra-wide SEM diagram for maximum compatibility...\n\n")
  
  # Create diagram
  main_diagram <- create_ultra_wide_sem_diagram()
  
  # Display diagram
  cat("Ultra-Wide SEM Framework:\n")
  print(main_diagram)
  
  # Save as PNG with ultra-wide dimensions
  tryCatch({
    if (require(DiagrammeRsvg, quietly = TRUE) && require(rsvg, quietly = TRUE)) {
      
      # Export with ultra-wide dimensions
      main_svg <- DiagrammeRsvg::export_svg(main_diagram)
      rsvg::rsvg_png(charToRaw(main_svg), "SEM_Framework_Ultra_Wide.png", width = 3200, height = 1200)
      
      cat("\n✅ Ultra-wide PNG file saved successfully:\n")
      cat("- SEM_Framework_Ultra_Wide.png (3200x1200 - Ultra Wide)\n\n")
      
      cat("Features:\n")
      cat("• Ultra-wide format (3200x1200)\n")
      cat("• Maximum spacing between elements\n")
      cat("• Guaranteed complete visibility\n")
      cat("• Clean layout with no overlaps\n")
      cat("• All hypotheses clearly labeled\n")
      
    } else {
      cat("DiagrammeRsvg and rsvg packages required for PNG export\n")
    }
    
  }, error = function(e) {
    cat("Error saving PNG files:", e$message, "\n")
  })
  
  return(main_diagram)
}

# ===============================================================================
# Execute Ultra-Wide Visualization
# ===============================================================================

cat("=== Ultra-Wide SEM Visualization ===\n\n")

# Generate the ultra-wide diagram
ultra_wide_diagram <- generate_ultra_wide_diagram()

cat("\n=== Ultra-Wide Visualization Complete ===\n")
cat("This version uses an ultra-wide format (3200x1200) to ensure\n")
cat("the complete diagram is visible without any cropping.\n\n")

cat("If you still experience cropping issues, the problem might be:\n")
cat("1. Image viewer limitations\n")
cat("2. Display resolution constraints\n")
cat("3. File size restrictions\n\n")

cat("Try opening the PNG file in different image viewers or\n")
cat("image editing software for best results.\n")
