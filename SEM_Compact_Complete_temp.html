---
title: grViz
header-include: |
  <link href="SEM_Compact_Complete_temp_files/htmltools-fill-*******/fill.css" rel="stylesheet" />
  <script src="SEM_Compact_Complete_temp_files/htmlwidgets-1.6.4/htmlwidgets.js"></script>
  <script src="SEM_Compact_Complete_temp_files/viz-1.8.2/viz.js"></script>
  <link href="SEM_Compact_Complete_temp_files/DiagrammeR-styles-0.2/styles.css" rel="stylesheet" />
  <script src="SEM_Compact_Complete_temp_files/grViz-binding-1.0.11/grViz.js"></script>
head: |2+

background-color: white

---
<div id="htmlwidget_container">
<div class="grViz html-widget html-fill-item" id="htmlwidget-c9694ae320eff85797cc" style="width:960px;height:500px;"></div>
</div>
<script type="application/json" data-for="htmlwidget-c9694ae320eff85797cc">{"x":{"diagram":"\n  digraph compact_sem {\n    \n    # Graph settings - compact layout\n    graph [layout = dot, \n           rankdir = LR, \n           bgcolor = white, \n           fontname = \"Arial\",\n           fontsize = 10,\n           margin = 0.3,\n           pad = 0.3,\n           ranksep = 1.2,\n           nodesep = 0.8,\n           concentrate = true]\n    \n    # Node styling - smaller but readable\n    node [shape = box, \n          style = \"filled,rounded\", \n          fontname = \"Arial\", \n          fontsize = 9,\n          width = 1.5,\n          height = 0.8,\n          margin = 0.1]\n    \n    # Edge styling - compact\n    edge [fontname = \"Arial\", \n          fontsize = 8,\n          labeldistance = 1.2]\n    \n    # ===== COMPACT RANK STRUCTURE =====\n    {rank = same; DC; RC}\n    {rank = same; SIC}\n    {rank = same; TBL_E; TBL_S; TBL_EN}\n    {rank = same; CA}\n    {rank = min; ED}\n    {rank = max; MCI}\n    \n    # ===== COMPACT NODE DEFINITIONS =====\n    \n    # Independent Variables\n    DC [label = \"Dynamic\\nCapabilities\\n(DC)\", \n        fillcolor = \"#E3F2FD\", color = \"#1976D2\"]\n    \n    RC [label = \"Relational\\nCoordination\\n(RC)\", \n        fillcolor = \"#E8F5E8\", color = \"#388E3C\"]\n    \n    # Mediator Variable\n    SIC [label = \"Service Innovation\\nCapability (SIC)\", \n         fillcolor = \"#FFF3E0\", color = \"#F57C00\"]\n    \n    # TBL Performance Dimensions\n    TBL_E [label = \"Economic\\nPerformance\\n(TBL-E)\", \n           fillcolor = \"#E8F5E8\", color = \"#4CAF50\"]\n    \n    TBL_S [label = \"Social\\nPerformance\\n(TBL-S)\", \n           fillcolor = \"#E8F5E8\", color = \"#4CAF50\"]\n    \n    TBL_EN [label = \"Environmental\\nPerformance\\n(TBL-EN)\", \n            fillcolor = \"#E8F5E8\", color = \"#4CAF50\"]\n    \n    # Dependent Variable\n    CA [label = \"Competitive\\nAdvantage\\n(CA)\", \n        fillcolor = \"#FCE4EC\", color = \"#C2185B\"]\n    \n    # Moderator Variables - compact\n    ED [label = \"Environmental\\nDynamism (ED)\", \n        fillcolor = \"#F3E5F5\", color = \"#7B1FA2\", shape = ellipse, width = 1.3, height = 0.7]\n    \n    MCI [label = \"Market Competition\\nIntensity (MCI)\", \n         fillcolor = \"#F3E5F5\", color = \"#7B1FA2\", shape = ellipse, width = 1.3, height = 0.7]\n    \n    # ===== MAIN STRUCTURAL PATHS =====\n    \n    # H1 & H2: Direct antecedent effects\n    DC -> SIC [label = \"H1\", color = \"#1976D2\", penwidth = 2.5, fontcolor = \"#1976D2\"]\n    RC -> SIC [label = \"H2\", color = \"#388E3C\", penwidth = 2.5, fontcolor = \"#388E3C\"]\n    \n    # H3a-c: SIC to TBL dimensions\n    SIC -> TBL_E [label = \"H3a\", color = \"#F57C00\", penwidth = 2.5, fontcolor = \"#F57C00\"]\n    SIC -> TBL_S [label = \"H3b\", color = \"#F57C00\", penwidth = 2.5, fontcolor = \"#F57C00\"]\n    SIC -> TBL_EN [label = \"H3c\", color = \"#F57C00\", penwidth = 2.5, fontcolor = \"#F57C00\"]\n    \n    # H4a-c: TBL dimensions to CA\n    TBL_E -> CA [label = \"H4a\", color = \"#4CAF50\", penwidth = 2.5, fontcolor = \"#4CAF50\"]\n    TBL_S -> CA [label = \"H4b\", color = \"#4CAF50\", penwidth = 2.5, fontcolor = \"#4CAF50\"]\n    TBL_EN -> CA [label = \"H4c\", color = \"#4CAF50\", penwidth = 2.5, fontcolor = \"#4CAF50\"]\n    \n    # H5a-b: Direct effects (dashed lines)\n    DC -> TBL_E [label = \"H5a\", color = \"#FF5722\", style = dashed, penwidth = 1.5, fontcolor = \"#FF5722\"]\n    DC -> TBL_S [label = \"H5a\", color = \"#FF5722\", style = dashed, penwidth = 1.5, fontcolor = \"#FF5722\"]\n    DC -> TBL_EN [label = \"H5a\", color = \"#FF5722\", style = dashed, penwidth = 1.5, fontcolor = \"#FF5722\"]\n    \n    RC -> TBL_E [label = \"H5b\", color = \"#FF5722\", style = dashed, penwidth = 1.5, fontcolor = \"#FF5722\"]\n    RC -> TBL_S [label = \"H5b\", color = \"#FF5722\", style = dashed, penwidth = 1.5, fontcolor = \"#FF5722\"]\n    RC -> TBL_EN [label = \"H5b\", color = \"#FF5722\", style = dashed, penwidth = 1.5, fontcolor = \"#FF5722\"]\n    \n    # H7: Moderation effects (simplified)\n    ED -> SIC [label = \"H7 Mod\", color = \"#7B1FA2\", style = dotted, penwidth = 2, fontcolor = \"#7B1FA2\"]\n    MCI -> CA [label = \"H7 Mod\", color = \"#7B1FA2\", style = dotted, penwidth = 2, fontcolor = \"#7B1FA2\"]\n    \n    # Covariances (simplified)\n    DC -> RC [dir = both, color = \"#9E9E9E\", style = dotted, penwidth = 1]\n    TBL_E -> TBL_S [dir = both, color = \"#9E9E9E\", style = dotted, penwidth = 0.5]\n    TBL_S -> TBL_EN [dir = both, color = \"#9E9E9E\", style = dotted, penwidth = 0.5]\n    \n    # Title\n    labelloc = \"t\"\n    label = \"SEM: Dynamic Capabilities and Triple Bottom Line Performance\"\n  }\n  ","config":{"engine":"dot","options":null}},"evals":[],"jsHooks":[]}</script>
<script type="application/htmlwidget-sizing" data-for="htmlwidget-c9694ae320eff85797cc">{"viewer":{"width":450,"height":350,"padding":15,"fill":true},"browser":{"width":960,"height":500,"padding":40,"fill":false}}</script>
