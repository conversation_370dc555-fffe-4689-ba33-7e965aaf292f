# ===============================================================================
# Structural Equation Model (SEM) Diagram Generator
# Research Framework: Dynamic Capabilities, Service Innovation, and Triple Bottom Line
# ===============================================================================

# Required packages installation
required_packages <- c("lavaan", "semPlot", "DiagrammeR", "ggplot2", "dplyr", 
                      "gridExtra", "RColorBrewer", "viridis")

# Install packages if not already installed
install_if_missing <- function(packages) {
  new_packages <- packages[!(packages %in% installed.packages()[,"Package"])]
  if(length(new_packages)) install.packages(new_packages)
}

install_if_missing(required_packages)

# Load required libraries
library(lavaan)
library(semPlot)
library(DiagrammeR)
library(ggplot2)
library(dplyr)
library(gridExtra)
library(RColorBrewer)
library(viridis)

# ===============================================================================
# 1. MODEL SPECIFICATION
# ===============================================================================

# Define the structural equation model based on research hypotheses
sem_model <- '
  # Measurement Models (if using latent variables)
  # Dynamic Capabilities (DC)
  DC =~ DC1 + DC2 + DC3
  
  # Relational Coordination (RC) 
  RC =~ RC1 + RC2 + RC3
  
  # Service Innovation Capability (SIC)
  SIC =~ SIC1 + SIC2 + SIC3
  
  # Triple Bottom Line Performance dimensions
  TBL_Economic =~ TBL_E1 + TBL_E2 + TBL_E3
  TBL_Social =~ TBL_S1 + TBL_S2 + TBL_S3
  TBL_Environmental =~ TBL_EN1 + TBL_EN2 + TBL_EN3
  
  # Competitive Advantage (CA)
  CA =~ CA1 + CA2 + CA3
  
  # Environmental Dynamism (ED) - Moderator
  ED =~ ED1 + ED2 + ED3
  
  # Market Competition Intensity (MCI) - Moderator
  MCI =~ MCI1 + MCI2 + MCI3
  
  # Structural Model - Direct Effects
  # H1: DC → SIC
  SIC ~ beta1*DC
  
  # H2: RC → SIC  
  SIC ~ beta2*RC
  
  # H3a-c: SIC → TBL dimensions
  TBL_Economic ~ beta3a*SIC
  TBL_Social ~ beta3b*SIC
  TBL_Environmental ~ beta3c*SIC
  
  # H4a-c: TBL dimensions → CA
  CA ~ beta4a*TBL_Economic + beta4b*TBL_Social + beta4c*TBL_Environmental
  
  # H5a-b: Direct effects DC/RC → TBL
  TBL_Economic ~ beta5a1*DC + beta5b1*RC
  TBL_Social ~ beta5a2*DC + beta5b2*RC
  TBL_Environmental ~ beta5a3*DC + beta5b3*RC
  
  # Moderation Effects (H7)
  # ED moderating DC/RC → SIC
  SIC ~ beta7a*DC:ED + beta7b*RC:ED
  
  # MCI moderating TBL → CA
  CA ~ beta7c*TBL_Economic:MCI + beta7d*TBL_Social:MCI + beta7e*TBL_Environmental:MCI
  
  # Covariances
  DC ~~ RC
  TBL_Economic ~~ TBL_Social
  TBL_Economic ~~ TBL_Environmental
  TBL_Social ~~ TBL_Environmental
  ED ~~ MCI
'

# ===============================================================================
# 2. SIMPLIFIED MODEL FOR VISUALIZATION
# ===============================================================================

# Create a simplified model for cleaner visualization
simple_model <- '
  # Direct paths (H1, H2)
  SIC ~ beta1*DC + beta2*RC
  
  # SIC to TBL dimensions (H3a-c)
  TBL_Economic ~ beta3a*SIC
  TBL_Social ~ beta3b*SIC
  TBL_Environmental ~ beta3c*SIC
  
  # TBL to CA (H4a-c)
  CA ~ beta4a*TBL_Economic + beta4b*TBL_Social + beta4c*TBL_Environmental
  
  # Direct effects (H5a-b)
  TBL_Economic ~ beta5a1*DC + beta5b1*RC
  TBL_Social ~ beta5a2*DC + beta5b2*RC
  TBL_Environmental ~ beta5a3*DC + beta5b3*RC
  
  # Covariances
  DC ~~ RC
  TBL_Economic ~~ TBL_Social
  TBL_Economic ~~ TBL_Environmental
  TBL_Social ~~ TBL_Environmental
'

# ===============================================================================
# 3. DIAGRAMMER VISUALIZATION FUNCTION
# ===============================================================================

create_sem_diagram <- function() {
  
  # Create the diagram using DiagrammeR
  diagram <- grViz("
  digraph SEM_Model {
    
    # Graph attributes
    graph [layout = dot, rankdir = LR, bgcolor = white, fontname = 'Arial']
    
    # Node attributes
    node [shape = box, style = filled, fontname = 'Arial', fontsize = 10]
    
    # Define node styles
    subgraph cluster_independent {
      label = 'Independent Variables'
      style = filled
      color = lightblue
      fontsize = 12
      
      DC [label = 'Dynamic Capabilities\\n(DC)\\n动态能力', fillcolor = lightcyan]
      RC [label = 'Relational Coordination\\n(RC)\\n关系协调', fillcolor = lightcyan]
    }
    
    subgraph cluster_mediator {
      label = 'Mediator'
      style = filled
      color = lightyellow
      fontsize = 12
      
      SIC [label = 'Service Innovation\\nCapability (SIC)\\n服务创新能力', fillcolor = lightyellow]
    }
    
    subgraph cluster_tbl {
      label = 'Triple Bottom Line Performance'
      style = filled
      color = lightgreen
      fontsize = 12
      
      TBL_E [label = 'Economic Performance\\n(TBL-E)\\n经济绩效', fillcolor = lightgreen]
      TBL_S [label = 'Social Performance\\n(TBL-S)\\n社会绩效', fillcolor = lightgreen]
      TBL_EN [label = 'Environmental Performance\\n(TBL-EN)\\n环境绩效', fillcolor = lightgreen]
    }
    
    subgraph cluster_dependent {
      label = 'Dependent Variable'
      style = filled
      color = lightpink
      fontsize = 12
      
      CA [label = 'Competitive Advantage\\n(CA)\\n竞争优势', fillcolor = lightpink]
    }
    
    subgraph cluster_moderators {
      label = 'Moderators'
      style = filled
      color = lavender
      fontsize = 12
      
      ED [label = 'Environmental\\nDynamism (ED)\\n环境动态性', fillcolor = lavender]
      MCI [label = 'Market Competition\\nIntensity (MCI)\\n市场竞争强度', fillcolor = lavender]
    }
    
    # Direct paths (H1, H2)
    DC -> SIC [label = 'β₁ (H1)', color = blue, penwidth = 2]
    RC -> SIC [label = 'β₂ (H2)', color = blue, penwidth = 2]
    
    # SIC to TBL (H3a-c)
    SIC -> TBL_E [label = 'β₃ₐ (H3a)', color = green, penwidth = 2]
    SIC -> TBL_S [label = 'β₃ᵦ (H3b)', color = green, penwidth = 2]
    SIC -> TBL_EN [label = 'β₃ᶜ (H3c)', color = green, penwidth = 2]
    
    # TBL to CA (H4a-c)
    TBL_E -> CA [label = 'β₄ₐ (H4a)', color = red, penwidth = 2]
    TBL_S -> CA [label = 'β₄ᵦ (H4b)', color = red, penwidth = 2]
    TBL_EN -> CA [label = 'β₄ᶜ (H4c)', color = red, penwidth = 2]
    
    # Direct effects (H5a-b)
    DC -> TBL_E [label = 'β₅ₐ₁ (H5a)', color = orange, style = dashed]
    DC -> TBL_S [label = 'β₅ₐ₂ (H5a)', color = orange, style = dashed]
    DC -> TBL_EN [label = 'β₅ₐ₃ (H5a)', color = orange, style = dashed]
    
    RC -> TBL_E [label = 'β₅ᵦ₁ (H5b)', color = orange, style = dashed]
    RC -> TBL_S [label = 'β₅ᵦ₂ (H5b)', color = orange, style = dashed]
    RC -> TBL_EN [label = 'β₅ᵦ₃ (H5b)', color = orange, style = dashed]
    
    # Moderation effects (H7) - shown as dotted lines
    ED -> SIC [label = 'Moderates\\nDC/RC→SIC\\n(H7)', color = purple, style = dotted, penwidth = 1]
    MCI -> CA [label = 'Moderates\\nTBL→CA\\n(H7)', color = purple, style = dotted, penwidth = 1]
    
    # Covariances
    DC -> RC [dir = both, label = 'Cov', color = gray, style = dotted]
    TBL_E -> TBL_S [dir = both, color = gray, style = dotted]
    TBL_E -> TBL_EN [dir = both, color = gray, style = dotted]
    TBL_S -> TBL_EN [dir = both, color = gray, style = dotted]
  }
  ")
  
  return(diagram)
}

# ===============================================================================
# 4. GENERATE AND DISPLAY THE DIAGRAM
# ===============================================================================

# Create the main SEM diagram
main_diagram <- create_sem_diagram()

# Display the diagram
print(main_diagram)

# Save the diagram as PNG
main_diagram %>%
  export_svg() %>%
  charToRaw() %>%
  rsvg::rsvg_png("SEM_Theoretical_Framework.png", width = 1200, height = 800)

cat("SEM diagram has been generated and saved as 'SEM_Theoretical_Framework.png'\n")

# ===============================================================================
# 5. ALTERNATIVE SEMPLOT VISUALIZATION
# ===============================================================================

# Create sample data for semPlot visualization
create_semplot_diagram <- function() {

  # Generate sample data (replace with your actual data)
  set.seed(123)
  n <- 200

  # Create sample dataset
  sample_data <- data.frame(
    DC1 = rnorm(n), DC2 = rnorm(n), DC3 = rnorm(n),
    RC1 = rnorm(n), RC2 = rnorm(n), RC3 = rnorm(n),
    SIC1 = rnorm(n), SIC2 = rnorm(n), SIC3 = rnorm(n),
    TBL_E1 = rnorm(n), TBL_E2 = rnorm(n), TBL_E3 = rnorm(n),
    TBL_S1 = rnorm(n), TBL_S2 = rnorm(n), TBL_S3 = rnorm(n),
    TBL_EN1 = rnorm(n), TBL_EN2 = rnorm(n), TBL_EN3 = rnorm(n),
    CA1 = rnorm(n), CA2 = rnorm(n), CA3 = rnorm(n),
    ED1 = rnorm(n), ED2 = rnorm(n), ED3 = rnorm(n),
    MCI1 = rnorm(n), MCI2 = rnorm(n), MCI3 = rnorm(n)
  )

  # Fit the model
  fit <- sem(simple_model, data = sample_data, estimator = "ML")

  # Create semPlot diagram
  semPaths(fit,
           what = "paths",
           whatLabels = "par",
           style = "lisrel",
           layout = "tree2",
           rotation = 2,
           curve = 1,
           nCharNodes = 8,
           nCharEdges = 5,
           sizeMan = 8,
           sizeLat = 10,
           edge.label.cex = 0.8,
           node.label.cex = 0.9,
           label.prop = 0.8,
           edge.color = "black",
           node.color = c("lightblue", "lightgreen", "lightpink"),
           borders = TRUE,
           title = "Structural Equation Model: Dynamic Capabilities and Triple Bottom Line",
           title.cex = 1.2,
           mar = c(3, 3, 3, 3))

  return(fit)
}

# ===============================================================================
# 6. CREATE LEGEND AND HYPOTHESIS TABLE
# ===============================================================================

create_hypothesis_table <- function() {

  # Create hypothesis summary table
  hypotheses <- data.frame(
    Hypothesis = c("H1", "H2", "H3a", "H3b", "H3c", "H4a", "H4b", "H4c",
                   "H5a", "H5b", "H6", "H7"),
    Relationship = c(
      "DC → SIC",
      "RC → SIC",
      "SIC → TBL-Economic",
      "SIC → TBL-Social",
      "SIC → TBL-Environmental",
      "TBL-Economic → CA",
      "TBL-Social → CA",
      "TBL-Environmental → CA",
      "DC → TBL (Direct)",
      "RC → TBL (Direct)",
      "DC/RC → SIC → TBL → CA (Sequential Mediation)",
      "ED moderates DC/RC→SIC; MCI moderates TBL→CA"
    ),
    Description = c(
      "Dynamic capabilities positively influence service innovation capability",
      "Relational coordination positively influences service innovation capability",
      "Service innovation capability positively influences economic performance",
      "Service innovation capability positively influences social performance",
      "Service innovation capability positively influences environmental performance",
      "Economic performance positively influences competitive advantage",
      "Social performance positively influences competitive advantage",
      "Environmental performance positively influences competitive advantage",
      "Dynamic capabilities have direct effects on TBL performance",
      "Relational coordination has direct effects on TBL performance",
      "Sequential mediation through SIC and TBL dimensions",
      "Environmental dynamism and market competition intensity moderate relationships"
    ),
    stringsAsFactors = FALSE
  )

  return(hypotheses)
}

# ===============================================================================
# 7. COMPREHENSIVE VISUALIZATION FUNCTION
# ===============================================================================

generate_complete_sem_analysis <- function() {

  cat("=== Generating Comprehensive SEM Analysis ===\n\n")

  # 1. Main theoretical diagram
  cat("1. Creating main theoretical framework diagram...\n")
  main_diagram <- create_sem_diagram()
  print(main_diagram)

  # 2. Hypothesis table
  cat("2. Creating hypothesis summary table...\n")
  hyp_table <- create_hypothesis_table()
  print(hyp_table)

  # 3. Model specifications
  cat("3. Model Specifications:\n")
  cat("Full Model:\n")
  cat(sem_model)
  cat("\n\nSimplified Model for Visualization:\n")
  cat(simple_model)

  # 4. Save outputs
  cat("\n4. Saving outputs...\n")

  # Save hypothesis table
  write.csv(hyp_table, "Research_Hypotheses_Table.csv", row.names = FALSE)

  # Save model specifications
  writeLines(sem_model, "Full_SEM_Model.txt")
  writeLines(simple_model, "Simple_SEM_Model.txt")

  cat("Analysis complete! Files saved:\n")
  cat("- SEM_Theoretical_Framework.png\n")
  cat("- Research_Hypotheses_Table.csv\n")
  cat("- Full_SEM_Model.txt\n")
  cat("- Simple_SEM_Model.txt\n")
}

# ===============================================================================
# 8. EXECUTE COMPLETE ANALYSIS
# ===============================================================================

# Run the complete analysis
generate_complete_sem_analysis()

# ===============================================================================
# 9. ADDITIONAL NOTES AND INSTRUCTIONS
# ===============================================================================

cat("\n=== USAGE INSTRUCTIONS ===\n")
cat("1. Install required packages by running the installation section\n")
cat("2. Run generate_complete_sem_analysis() to create all outputs\n")
cat("3. The main diagram shows the complete theoretical framework\n")
cat("4. Modify colors, layout, or labels as needed for your publication\n")
cat("5. Replace sample data with your actual dataset for empirical analysis\n\n")

cat("=== LEGEND FOR DIAGRAM ===\n")
cat("• Solid blue lines: Direct antecedent relationships (H1, H2)\n")
cat("• Solid green lines: Mediation paths through SIC (H3a-c)\n")
cat("• Solid red lines: TBL to competitive advantage (H4a-c)\n")
cat("• Dashed orange lines: Direct effects bypassing SIC (H5a-b)\n")
cat("• Dotted purple lines: Moderation effects (H7)\n")
cat("• Dotted gray lines: Covariances between variables\n\n")

cat("=== VARIABLE ABBREVIATIONS ===\n")
cat("DC = Dynamic Capabilities (动态能力)\n")
cat("RC = Relational Coordination (关系协调)\n")
cat("SIC = Service Innovation Capability (服务创新能力)\n")
cat("TBL-E = Economic Performance (经济绩效)\n")
cat("TBL-S = Social Performance (社会绩效)\n")
cat("TBL-EN = Environmental Performance (环境绩效)\n")
cat("CA = Competitive Advantage (竞争优势)\n")
cat("ED = Environmental Dynamism (环境动态性)\n")
cat("MCI = Market Competition Intensity (市场竞争强度)\n")
