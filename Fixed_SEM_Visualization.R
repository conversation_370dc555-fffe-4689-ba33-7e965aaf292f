# ===============================================================================
# 修复版SEM可视化 - 解决字符显示问题
# Fixed SEM Visualization - Resolving Character Display Issues
# ===============================================================================

library(DiagrammeR)

# ===============================================================================
# 修复字符显示的SEM图表生成器
# ===============================================================================

create_fixed_sem_diagram <- function() {
  
  diagram <- grViz("
  digraph fixed_sem {
    
    # 图表设置 - 增加分辨率和字体大小
    graph [layout = dot, 
           rankdir = LR, 
           bgcolor = white, 
           fontname = 'SimSun',
           fontsize = 14,
           dpi = 300,
           size = '16,10!']
    
    # 节点样式 - 增大节点尺寸以容纳更多文字
    node [shape = box, 
          style = 'filled,rounded', 
          fontname = 'SimSun', 
          fontsize = 11,
          width = 2.2,
          height = 1.2,
          margin = 0.15]
    
    # 边样式
    edge [fontname = 'SimSun', 
          fontsize = 10,
          labeldistance = 2.5]
    
    # ===== 层级结构 =====
    {rank = same; DC; RC}
    {rank = same; SIC}
    {rank = same; TBL_E; TBL_S; TBL_EN}
    {rank = same; CA}
    {rank = min; ED; MCI}
    
    # ===== 节点定义 - 使用更清晰的标签 =====
    
    # 自变量
    DC [label = 'Dynamic Capabilities\\n动态能力\\n(DC)', 
        fillcolor = '#E3F2FD', color = '#1976D2', fontsize = 12]
    
    RC [label = 'Relational Coordination\\n关系协调\\n(RC)', 
        fillcolor = '#E8F5E8', color = '#388E3C', fontsize = 12]
    
    # 中介变量
    SIC [label = 'Service Innovation\\nCapability\\n服务创新能力\\n(SIC)', 
         fillcolor = '#FFF3E0', color = '#F57C00', fontsize = 12]
    
    # TBL绩效维度
    TBL_E [label = 'Economic\\nPerformance\\n经济绩效\\n(TBL-E)', 
           fillcolor = '#E8F5E8', color = '#4CAF50', fontsize = 11]
    
    TBL_S [label = 'Social\\nPerformance\\n社会绩效\\n(TBL-S)', 
           fillcolor = '#E8F5E8', color = '#4CAF50', fontsize = 11]
    
    TBL_EN [label = 'Environmental\\nPerformance\\n环境绩效\\n(TBL-EN)', 
            fillcolor = '#E8F5E8', color = '#4CAF50', fontsize = 11]
    
    # 因变量
    CA [label = 'Competitive\\nAdvantage\\n竞争优势\\n(CA)', 
        fillcolor = '#FCE4EC', color = '#C2185B', fontsize = 12]
    
    # 调节变量
    ED [label = 'Environmental\\nDynamism\\n环境动态性\\n(ED)', 
        fillcolor = '#F3E5F5', color = '#7B1FA2', shape = ellipse, fontsize = 11]
    
    MCI [label = 'Market Competition\\nIntensity\\n市场竞争强度\\n(MCI)', 
         fillcolor = '#F3E5F5', color = '#7B1FA2', shape = ellipse, fontsize = 11]
    
    # ===== 结构关系 - 使用更清晰的标签 =====
    
    # H1 & H2: 直接前因效应
    DC -> SIC [label = 'H1\\nβ1', color = '#1976D2', penwidth = 3, fontsize = 10, fontcolor = '#1976D2']
    RC -> SIC [label = 'H2\\nβ2', color = '#388E3C', penwidth = 3, fontsize = 10, fontcolor = '#388E3C']
    
    # H3a-c: SIC到TBL维度
    SIC -> TBL_E [label = 'H3a\\nβ3a', color = '#F57C00', penwidth = 3, fontsize = 10, fontcolor = '#F57C00']
    SIC -> TBL_S [label = 'H3b\\nβ3b', color = '#F57C00', penwidth = 3, fontsize = 10, fontcolor = '#F57C00']
    SIC -> TBL_EN [label = 'H3c\\nβ3c', color = '#F57C00', penwidth = 3, fontsize = 10, fontcolor = '#F57C00']
    
    # H4a-c: TBL维度到CA
    TBL_E -> CA [label = 'H4a\\nβ4a', color = '#4CAF50', penwidth = 3, fontsize = 10, fontcolor = '#4CAF50']
    TBL_S -> CA [label = 'H4b\\nβ4b', color = '#4CAF50', penwidth = 3, fontsize = 10, fontcolor = '#4CAF50']
    TBL_EN -> CA [label = 'H4c\\nβ4c', color = '#4CAF50', penwidth = 3, fontsize = 10, fontcolor = '#4CAF50']
    
    # H5a-b: 直接效应 (虚线)
    DC -> TBL_E [label = 'H5a', color = '#FF5722', style = dashed, penwidth = 2, fontsize = 9, fontcolor = '#FF5722']
    DC -> TBL_S [label = 'H5a', color = '#FF5722', style = dashed, penwidth = 2, fontsize = 9, fontcolor = '#FF5722']
    DC -> TBL_EN [label = 'H5a', color = '#FF5722', style = dashed, penwidth = 2, fontsize = 9, fontcolor = '#FF5722']
    
    RC -> TBL_E [label = 'H5b', color = '#FF5722', style = dashed, penwidth = 2, fontsize = 9, fontcolor = '#FF5722']
    RC -> TBL_S [label = 'H5b', color = '#FF5722', style = dashed, penwidth = 2, fontsize = 9, fontcolor = '#FF5722']
    RC -> TBL_EN [label = 'H5b', color = '#FF5722', style = dashed, penwidth = 2, fontsize = 9, fontcolor = '#FF5722']
    
    # H7: 调节效应 (点线)
    ED -> SIC [label = 'H7\\nModerates\\nDC/RC→SIC', color = '#7B1FA2', style = dotted, penwidth = 2.5, fontsize = 9, fontcolor = '#7B1FA2']
    MCI -> CA [label = 'H7\\nModerates\\nTBL→CA', color = '#7B1FA2', style = dotted, penwidth = 2.5, fontsize = 9, fontcolor = '#7B1FA2']
    
    # 协方差 (双向点线)
    DC -> RC [dir = both, color = '#9E9E9E', style = dotted, penwidth = 1.5]
    TBL_E -> TBL_S [dir = both, color = '#9E9E9E', style = dotted, penwidth = 1]
    TBL_E -> TBL_EN [dir = both, color = '#9E9E9E', style = dotted, penwidth = 1]
    TBL_S -> TBL_EN [dir = both, color = '#9E9E9E', style = dotted, penwidth = 1]
    
    # 标题
    labelloc = 't'
    label = 'Structural Equation Model: Dynamic Capabilities and Triple Bottom Line Performance\\n结构方程模型：动态能力与三重底线绩效'
  }
  ")
  
  return(diagram)
}

# ===============================================================================
# 创建改进的图例
# ===============================================================================

create_fixed_legend <- function() {
  
  legend <- grViz("
  digraph fixed_legend {
    
    graph [layout = dot, rankdir = TB, bgcolor = white, fontname = 'SimSun', fontsize = 12]
    node [shape = plaintext, fontname = 'SimSun', fontsize = 12]
    
    title [label = '图例说明 / Legend\\n\\n路径类型 / Path Types:', fontsize = 14]
    
    # 路径类型说明
    p1 [label = '━━━ 直接前因效应 Direct Antecedent Effects (H1, H2)', fontcolor = '#1976D2']
    p2 [label = '━━━ SIC中介效应 Mediation through SIC (H3a-c)', fontcolor = '#F57C00']
    p3 [label = '━━━ TBL到竞争优势 TBL to Competitive Advantage (H4a-c)', fontcolor = '#4CAF50']
    p4 [label = '┅┅┅ 直接效应 Direct Effects (H5a-b)', fontcolor = '#FF5722']
    p5 [label = '⋯⋯⋯ 调节效应 Moderation Effects (H7)', fontcolor = '#7B1FA2']
    p6 [label = '⋯⋯⋯ 协方差 Covariances', fontcolor = '#9E9E9E']
    
    blank1 [label = '\\n变量类型 / Variable Types:', fontsize = 14]
    
    # 变量类型说明
    v1 [label = '■ 蓝色方框：自变量 Independent Variables (DC, RC)', fontcolor = '#1976D2']
    v2 [label = '■ 橙色方框：中介变量 Mediator Variable (SIC)', fontcolor = '#F57C00']
    v3 [label = '■ 绿色方框：TBL绩效维度 TBL Performance Dimensions', fontcolor = '#4CAF50']
    v4 [label = '■ 粉色方框：因变量 Dependent Variable (CA)', fontcolor = '#C2185B']
    v5 [label = '● 紫色椭圆：调节变量 Moderator Variables (ED, MCI)', fontcolor = '#7B1FA2']
    
    blank2 [label = '\\n假设编号说明 / Hypothesis Codes:', fontsize = 14]
    
    # 假设说明
    h1 [label = 'H1: DC → SIC (动态能力 → 服务创新能力)']
    h2 [label = 'H2: RC → SIC (关系协调 → 服务创新能力)']
    h3 [label = 'H3: SIC → TBL (服务创新能力 → 三重底线绩效)']
    h4 [label = 'H4: TBL → CA (三重底线绩效 → 竞争优势)']
    h5 [label = 'H5: DC/RC → TBL (直接效应)']
    h6 [label = 'H6: DC/RC → SIC → TBL → CA (序列中介)']
    h7 [label = 'H7: 调节效应 (ED调节DC/RC→SIC; MCI调节TBL→CA)']
    
    # 布局
    title -> p1 -> p2 -> p3 -> p4 -> p5 -> p6 -> blank1 -> v1 -> v2 -> v3 -> v4 -> v5 -> blank2 -> h1 -> h2 -> h3 -> h4 -> h5 -> h6 -> h7
  }
  ")
  
  return(legend)
}

# ===============================================================================
# 生成修复版图表
# ===============================================================================

generate_fixed_diagrams <- function() {
  
  cat("生成修复版SEM图表...\n")
  cat("Generating fixed SEM diagrams...\n\n")
  
  # 创建图表
  main_diagram <- create_fixed_sem_diagram()
  legend_diagram <- create_fixed_legend()
  
  # 显示图表
  cat("主要框架图:\n")
  print(main_diagram)
  
  cat("\n图例说明:\n")
  print(legend_diagram)
  
  # 保存为PNG
  tryCatch({
    if (require(DiagrammeRsvg, quietly = TRUE) && require(rsvg, quietly = TRUE)) {
      
      # 导出主图表 - 更高分辨率
      main_svg <- DiagrammeRsvg::export_svg(main_diagram)
      rsvg::rsvg_png(charToRaw(main_svg), "SEM_Framework_Fixed_Main.png", width = 1600, height = 1000)
      
      # 导出图例 - 更高分辨率
      legend_svg <- DiagrammeRsvg::export_svg(legend_diagram)
      rsvg::rsvg_png(charToRaw(legend_svg), "SEM_Framework_Fixed_Legend.png", width = 800, height = 1200)
      
      cat("\n✅ 修复版PNG文件保存成功:\n")
      cat("- SEM_Framework_Fixed_Main.png (1600x1000)\n")
      cat("- SEM_Framework_Fixed_Legend.png (800x1200)\n\n")
      
      cat("修复内容:\n")
      cat("• 增大节点尺寸以完整显示文字\n")
      cat("• 使用SimSun字体支持中文显示\n")
      cat("• 增加字体大小和边距\n")
      cat("• 提高图片分辨率\n")
      cat("• 优化标签布局\n")
      
    } else {
      cat("需要安装DiagrammeRsvg和rsvg包来保存PNG文件\n")
    }
    
  }, error = function(e) {
    cat("保存PNG时出错:", e$message, "\n")
  })
  
  return(list(main = main_diagram, legend = legend_diagram))
}

# ===============================================================================
# 执行修复版可视化
# ===============================================================================

cat("=== 修复版SEM可视化 ===\n")
cat("=== Fixed SEM Visualization ===\n\n")

# 生成修复版图表
fixed_diagrams <- generate_fixed_diagrams()

cat("\n=== 修复完成 ===\n")
cat("主要改进:\n")
cat("1. 节点尺寸增大 - 确保文字完整显示\n")
cat("2. 字体优化 - 使用SimSun支持中文\n")
cat("3. 分辨率提升 - 1600x1000高清输出\n")
cat("4. 标签清晰 - 假设编号和系数标识\n")
cat("5. 布局优化 - 更好的视觉效果\n\n")

cat("现在的图表应该能够完整显示所有字符！\n")
