# ===============================================================================
# Complete SEM Visualization - Fixed Layout and Export
# Ensures full diagram is visible in exported PNG
# ===============================================================================

library(DiagrammeR)

# ===============================================================================
# Complete SEM Diagram with Optimized Layout
# ===============================================================================

create_complete_sem_diagram <- function() {
  
  diagram <- grViz("
  digraph complete_sem {
    
    # Graph settings - optimized for complete visibility
    graph [layout = dot, 
           rankdir = LR, 
           bgcolor = white, 
           fontname = 'Arial',
           fontsize = 12,
           margin = 0.5,
           pad = 0.5,
           ranksep = 1.5,
           nodesep = 1.0]
    
    # Node styling - compact but readable
    node [shape = box, 
          style = 'filled,rounded', 
          fontname = 'Arial', 
          fontsize = 10,
          width = 1.8,
          height = 0.8,
          margin = 0.1]
    
    # Edge styling
    edge [fontname = 'Arial', 
          fontsize = 8,
          labeldistance = 1.5]
    
    # ===== RANK STRUCTURE FOR PROPER LAYOUT =====
    {rank = same; DC; RC}
    {rank = same; SIC}
    {rank = same; TBL_E; TBL_S; TBL_EN}
    {rank = same; CA}
    {rank = min; ED}
    {rank = max; MCI}
    
    # ===== NODE DEFINITIONS =====
    
    # Independent Variables
    DC [label = 'Dynamic\\nCapabilities\\n(DC)', 
        fillcolor = '#E3F2FD', color = '#1976D2']
    
    RC [label = 'Relational\\nCoordination\\n(RC)', 
        fillcolor = '#E8F5E8', color = '#388E3C']
    
    # Mediator Variable
    SIC [label = 'Service Innovation\\nCapability\\n(SIC)', 
         fillcolor = '#FFF3E0', color = '#F57C00']
    
    # TBL Performance Dimensions
    TBL_E [label = 'Economic\\nPerformance\\n(TBL-E)', 
           fillcolor = '#E8F5E8', color = '#4CAF50']
    
    TBL_S [label = 'Social\\nPerformance\\n(TBL-S)', 
           fillcolor = '#E8F5E8', color = '#4CAF50']
    
    TBL_EN [label = 'Environmental\\nPerformance\\n(TBL-EN)', 
            fillcolor = '#E8F5E8', color = '#4CAF50']
    
    # Dependent Variable
    CA [label = 'Competitive\\nAdvantage\\n(CA)', 
        fillcolor = '#FCE4EC', color = '#C2185B']
    
    # Moderator Variables - positioned to avoid overlap
    ED [label = 'Environmental\\nDynamism\\n(ED)', 
        fillcolor = '#F3E5F5', color = '#7B1FA2', shape = ellipse, width = 1.5, height = 0.7]
    
    MCI [label = 'Market Competition\\nIntensity\\n(MCI)', 
         fillcolor = '#F3E5F5', color = '#7B1FA2', shape = ellipse, width = 1.5, height = 0.7]
    
    # ===== STRUCTURAL RELATIONSHIPS =====
    
    # H1 & H2: Direct antecedent effects
    DC -> SIC [label = 'H1', color = '#1976D2', penwidth = 2.5, fontcolor = '#1976D2']
    RC -> SIC [label = 'H2', color = '#388E3C', penwidth = 2.5, fontcolor = '#388E3C']
    
    # H3a-c: SIC to TBL dimensions
    SIC -> TBL_E [label = 'H3a', color = '#F57C00', penwidth = 2.5, fontcolor = '#F57C00']
    SIC -> TBL_S [label = 'H3b', color = '#F57C00', penwidth = 2.5, fontcolor = '#F57C00']
    SIC -> TBL_EN [label = 'H3c', color = '#F57C00', penwidth = 2.5, fontcolor = '#F57C00']
    
    # H4a-c: TBL dimensions to CA
    TBL_E -> CA [label = 'H4a', color = '#4CAF50', penwidth = 2.5, fontcolor = '#4CAF50']
    TBL_S -> CA [label = 'H4b', color = '#4CAF50', penwidth = 2.5, fontcolor = '#4CAF50']
    TBL_EN -> CA [label = 'H4c', color = '#4CAF50', penwidth = 2.5, fontcolor = '#4CAF50']
    
    # H5a-b: Direct effects (dashed lines)
    DC -> TBL_E [label = 'H5a', color = '#FF5722', style = dashed, penwidth = 1.5, fontcolor = '#FF5722']
    DC -> TBL_S [label = 'H5a', color = '#FF5722', style = dashed, penwidth = 1.5, fontcolor = '#FF5722']
    DC -> TBL_EN [label = 'H5a', color = '#FF5722', style = dashed, penwidth = 1.5, fontcolor = '#FF5722']
    
    RC -> TBL_E [label = 'H5b', color = '#FF5722', style = dashed, penwidth = 1.5, fontcolor = '#FF5722']
    RC -> TBL_S [label = 'H5b', color = '#FF5722', style = dashed, penwidth = 1.5, fontcolor = '#FF5722']
    RC -> TBL_EN [label = 'H5b', color = '#FF5722', style = dashed, penwidth = 1.5, fontcolor = '#FF5722']
    
    # H7: Moderation effects (dotted lines) - simplified to avoid overlap
    ED -> SIC [label = 'H7\\nMod', color = '#7B1FA2', style = dotted, penwidth = 2, fontcolor = '#7B1FA2']
    MCI -> CA [label = 'H7\\nMod', color = '#7B1FA2', style = dotted, penwidth = 2, fontcolor = '#7B1FA2']
    
    # Covariances (simplified)
    DC -> RC [dir = both, color = '#9E9E9E', style = dotted, penwidth = 1]
    TBL_E -> TBL_S [dir = both, color = '#9E9E9E', style = dotted, penwidth = 0.5]
    TBL_S -> TBL_EN [dir = both, color = '#9E9E9E', style = dotted, penwidth = 0.5]
    
    # Title
    labelloc = 't'
    label = 'Structural Equation Model: Dynamic Capabilities and Triple Bottom Line Performance'
  }
  ")
  
  return(diagram)
}

# ===============================================================================
# Simplified Legend for Better Fit
# ===============================================================================

create_complete_legend <- function() {
  
  legend <- grViz("
  digraph complete_legend {
    
    graph [layout = dot, rankdir = TB, bgcolor = white, fontname = 'Arial']
    node [shape = plaintext, fontname = 'Arial', fontsize = 11]
    
    title [label = 'LEGEND', fontsize = 14, fontcolor = 'black']
    
    # Path types
    p1 [label = '━━━ Direct Effects (H1, H2): Blue/Green', fontcolor = '#1976D2']
    p2 [label = '━━━ Mediation (H3a-c): Orange', fontcolor = '#F57C00']
    p3 [label = '━━━ TBL→CA (H4a-c): Green', fontcolor = '#4CAF50']
    p4 [label = '┅┅┅ Direct Effects (H5a-b): Red', fontcolor = '#FF5722']
    p5 [label = '⋯⋯⋯ Moderation (H7): Purple', fontcolor = '#7B1FA2']
    p6 [label = '⋯⋯⋯ Covariances: Gray', fontcolor = '#9E9E9E']
    
    blank1 [label = ' ', fontsize = 8]
    
    # Variables
    v1 [label = 'DC = Dynamic Capabilities', fontcolor = '#1976D2']
    v2 [label = 'RC = Relational Coordination', fontcolor = '#388E3C']
    v3 [label = 'SIC = Service Innovation Capability', fontcolor = '#F57C00']
    v4 [label = 'TBL-E/S/EN = Triple Bottom Line Performance', fontcolor = '#4CAF50']
    v5 [label = 'CA = Competitive Advantage', fontcolor = '#C2185B']
    v6 [label = 'ED = Environmental Dynamism (Moderator)', fontcolor = '#7B1FA2']
    v7 [label = 'MCI = Market Competition Intensity (Moderator)', fontcolor = '#7B1FA2']
    
    blank2 [label = ' ', fontsize = 8]
    
    # Key hypothesis
    h6 [label = 'H6: Sequential Mediation DC/RC → SIC → TBL → CA', fontcolor = 'black']
    
    # Layout
    title -> p1 -> p2 -> p3 -> p4 -> p5 -> p6 -> blank1 -> v1 -> v2 -> v3 -> v4 -> v5 -> v6 -> v7 -> blank2 -> h6
  }
  ")
  
  return(legend)
}

# ===============================================================================
# Generate Complete Diagrams with Proper Sizing
# ===============================================================================

generate_complete_diagrams <- function() {
  
  cat("Generating complete SEM diagrams with proper sizing...\n\n")
  
  # Create diagrams
  main_diagram <- create_complete_sem_diagram()
  legend_diagram <- create_complete_legend()
  
  # Display diagrams
  cat("Complete SEM Framework:\n")
  print(main_diagram)
  
  cat("\nLegend:\n")
  print(legend_diagram)
  
  # Save as PNG with larger dimensions to ensure complete visibility
  tryCatch({
    if (require(DiagrammeRsvg, quietly = TRUE) && require(rsvg, quietly = TRUE)) {
      
      # Export main diagram - much larger dimensions
      main_svg <- DiagrammeRsvg::export_svg(main_diagram)
      rsvg::rsvg_png(charToRaw(main_svg), "SEM_Framework_Complete_Main.png", width = 2400, height = 1600)
      
      # Export legend
      legend_svg <- DiagrammeRsvg::export_svg(legend_diagram)
      rsvg::rsvg_png(charToRaw(legend_svg), "SEM_Framework_Complete_Legend.png", width = 800, height = 1000)
      
      cat("\n✅ Complete PNG files saved successfully:\n")
      cat("- SEM_Framework_Complete_Main.png (2400x1600 - Extra Large)\n")
      cat("- SEM_Framework_Complete_Legend.png (800x1000)\n\n")
      
      cat("Improvements:\n")
      cat("• Much larger export dimensions (2400x1600)\n")
      cat("• Optimized layout with proper spacing\n")
      cat("• Simplified labels to prevent overlap\n")
      cat("• Better rank separation\n")
      cat("• Complete diagram visibility guaranteed\n")
      
    } else {
      cat("DiagrammeRsvg and rsvg packages required for PNG export\n")
    }
    
  }, error = function(e) {
    cat("Error saving PNG files:", e$message, "\n")
  })
  
  return(list(main = main_diagram, legend = legend_diagram))
}

# ===============================================================================
# Execute Complete Visualization
# ===============================================================================

cat("=== Complete SEM Visualization (Fixed Layout) ===\n\n")

# Generate the complete diagrams
complete_diagrams <- generate_complete_diagrams()

cat("\n=== Complete Visualization Generated ===\n")
cat("The main diagram should now be fully visible!\n\n")

cat("Key Features:\n")
cat("• All variables and relationships visible\n")
cat("• Proper spacing and layout\n")
cat("• High resolution (2400x1600)\n")
cat("• Clean English labels\n")
cat("• Color-coded hypothesis paths\n")
cat("• Sequential mediation clearly shown\n\n")

cat("If the diagram is still cut off, try opening the PNG file\n")
cat("in an image viewer that supports large images.\n")
