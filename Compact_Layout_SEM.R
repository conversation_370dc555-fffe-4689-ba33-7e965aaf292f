# ===============================================================================
# Compact Layout SEM - Ensures Complete Visibility
# Uses a more compact layout to fit within export boundaries
# ===============================================================================

library(DiagrammeR)

# ===============================================================================
# Compact SEM Diagram with Guaranteed Complete Display
# ===============================================================================

create_compact_sem_diagram <- function() {
  
  diagram <- grViz("
  digraph compact_sem {
    
    # Graph settings - compact layout
    graph [layout = dot, 
           rankdir = LR, 
           bgcolor = white, 
           fontname = 'Arial',
           fontsize = 10,
           margin = 0.3,
           pad = 0.3,
           ranksep = 1.2,
           nodesep = 0.8,
           concentrate = true]
    
    # Node styling - smaller but readable
    node [shape = box, 
          style = 'filled,rounded', 
          fontname = 'Arial', 
          fontsize = 9,
          width = 1.5,
          height = 0.8,
          margin = 0.1]
    
    # Edge styling - compact
    edge [fontname = 'Arial', 
          fontsize = 8,
          labeldistance = 1.2]
    
    # ===== COMPACT RANK STRUCTURE =====
    {rank = same; DC; RC}
    {rank = same; SIC}
    {rank = same; TBL_E; TBL_S; TBL_EN}
    {rank = same; CA}
    {rank = min; ED}
    {rank = max; MCI}
    
    # ===== COMPACT NODE DEFINITIONS =====
    
    # Independent Variables
    DC [label = 'Dynamic\\nCapabilities\\n(DC)', 
        fillcolor = '#E3F2FD', color = '#1976D2']
    
    RC [label = 'Relational\\nCoordination\\n(RC)', 
        fillcolor = '#E8F5E8', color = '#388E3C']
    
    # Mediator Variable
    SIC [label = 'Service Innovation\\nCapability (SIC)', 
         fillcolor = '#FFF3E0', color = '#F57C00']
    
    # TBL Performance Dimensions
    TBL_E [label = 'Economic\\nPerformance\\n(TBL-E)', 
           fillcolor = '#E8F5E8', color = '#4CAF50']
    
    TBL_S [label = 'Social\\nPerformance\\n(TBL-S)', 
           fillcolor = '#E8F5E8', color = '#4CAF50']
    
    TBL_EN [label = 'Environmental\\nPerformance\\n(TBL-EN)', 
            fillcolor = '#E8F5E8', color = '#4CAF50']
    
    # Dependent Variable
    CA [label = 'Competitive\\nAdvantage\\n(CA)', 
        fillcolor = '#FCE4EC', color = '#C2185B']
    
    # Moderator Variables - compact
    ED [label = 'Environmental\\nDynamism (ED)', 
        fillcolor = '#F3E5F5', color = '#7B1FA2', shape = ellipse, width = 1.3, height = 0.7]
    
    MCI [label = 'Market Competition\\nIntensity (MCI)', 
         fillcolor = '#F3E5F5', color = '#7B1FA2', shape = ellipse, width = 1.3, height = 0.7]
    
    # ===== MAIN STRUCTURAL PATHS =====
    
    # H1 & H2: Direct antecedent effects
    DC -> SIC [label = 'H1', color = '#1976D2', penwidth = 2.5, fontcolor = '#1976D2']
    RC -> SIC [label = 'H2', color = '#388E3C', penwidth = 2.5, fontcolor = '#388E3C']
    
    # H3a-c: SIC to TBL dimensions
    SIC -> TBL_E [label = 'H3a', color = '#F57C00', penwidth = 2.5, fontcolor = '#F57C00']
    SIC -> TBL_S [label = 'H3b', color = '#F57C00', penwidth = 2.5, fontcolor = '#F57C00']
    SIC -> TBL_EN [label = 'H3c', color = '#F57C00', penwidth = 2.5, fontcolor = '#F57C00']
    
    # H4a-c: TBL dimensions to CA
    TBL_E -> CA [label = 'H4a', color = '#4CAF50', penwidth = 2.5, fontcolor = '#4CAF50']
    TBL_S -> CA [label = 'H4b', color = '#4CAF50', penwidth = 2.5, fontcolor = '#4CAF50']
    TBL_EN -> CA [label = 'H4c', color = '#4CAF50', penwidth = 2.5, fontcolor = '#4CAF50']
    
    # H5a-b: Direct effects (dashed lines)
    DC -> TBL_E [label = 'H5a', color = '#FF5722', style = dashed, penwidth = 1.5, fontcolor = '#FF5722']
    DC -> TBL_S [label = 'H5a', color = '#FF5722', style = dashed, penwidth = 1.5, fontcolor = '#FF5722']
    DC -> TBL_EN [label = 'H5a', color = '#FF5722', style = dashed, penwidth = 1.5, fontcolor = '#FF5722']
    
    RC -> TBL_E [label = 'H5b', color = '#FF5722', style = dashed, penwidth = 1.5, fontcolor = '#FF5722']
    RC -> TBL_S [label = 'H5b', color = '#FF5722', style = dashed, penwidth = 1.5, fontcolor = '#FF5722']
    RC -> TBL_EN [label = 'H5b', color = '#FF5722', style = dashed, penwidth = 1.5, fontcolor = '#FF5722']
    
    # H7: Moderation effects (simplified)
    ED -> SIC [label = 'H7 Mod', color = '#7B1FA2', style = dotted, penwidth = 2, fontcolor = '#7B1FA2']
    MCI -> CA [label = 'H7 Mod', color = '#7B1FA2', style = dotted, penwidth = 2, fontcolor = '#7B1FA2']
    
    # Covariances (simplified)
    DC -> RC [dir = both, color = '#9E9E9E', style = dotted, penwidth = 1]
    TBL_E -> TBL_S [dir = both, color = '#9E9E9E', style = dotted, penwidth = 0.5]
    TBL_S -> TBL_EN [dir = both, color = '#9E9E9E', style = dotted, penwidth = 0.5]
    
    # Title
    labelloc = 't'
    label = 'SEM: Dynamic Capabilities and Triple Bottom Line Performance'
  }
  ")
  
  return(diagram)
}

# ===============================================================================
# Alternative Export Method
# ===============================================================================

export_complete_diagram <- function(diagram, filename_base) {
  
  cat("Attempting multiple export methods...\n")
  
  # Method 1: Standard export with various sizes
  tryCatch({
    if (require(DiagrammeRsvg, quietly = TRUE) && require(rsvg, quietly = TRUE)) {
      
      main_svg <- DiagrammeRsvg::export_svg(diagram)
      
      # Try different aspect ratios and sizes
      rsvg::rsvg_png(charToRaw(main_svg), paste0(filename_base, "_4x3.png"), width = 4800, height = 3600)
      rsvg::rsvg_png(charToRaw(main_svg), paste0(filename_base, "_16x9.png"), width = 5120, height = 2880)
      rsvg::rsvg_png(charToRaw(main_svg), paste0(filename_base, "_square.png"), width = 4000, height = 4000)
      rsvg::rsvg_png(charToRaw(main_svg), paste0(filename_base, "_wide.png"), width = 6000, height = 2000)
      
      cat("✅ Multiple PNG files exported:\n")
      cat(paste0("- ", filename_base, "_4x3.png (4800x3600)\n"))
      cat(paste0("- ", filename_base, "_16x9.png (5120x2880)\n"))
      cat(paste0("- ", filename_base, "_square.png (4000x4000)\n"))
      cat(paste0("- ", filename_base, "_wide.png (6000x2000)\n"))
      
    }
  }, error = function(e) {
    cat("Standard export failed:", e$message, "\n")
  })
  
  # Method 2: Try webshot if available
  tryCatch({
    if (require(webshot, quietly = TRUE)) {
      
      # Save as HTML first
      htmlwidgets::saveWidget(diagram, paste0(filename_base, "_temp.html"), selfcontained = TRUE)
      
      # Convert to PNG
      webshot::webshot(paste0(filename_base, "_temp.html"), 
                      paste0(filename_base, "_webshot.png"),
                      vwidth = 1600, vheight = 1200, zoom = 2)
      
      # Clean up
      file.remove(paste0(filename_base, "_temp.html"))
      
      cat(paste0("✅ Webshot export: ", filename_base, "_webshot.png\n"))
      
    }
  }, error = function(e) {
    cat("Webshot export failed:", e$message, "\n")
  })
}

# ===============================================================================
# Generate Compact Diagram
# ===============================================================================

generate_compact_diagram <- function() {
  
  cat("Generating compact SEM diagram with guaranteed complete display...\n\n")
  
  # Create compact diagram
  compact_diagram <- create_compact_sem_diagram()
  
  # Display diagram
  cat("Compact SEM Framework:\n")
  print(compact_diagram)
  
  # Export using multiple methods
  export_complete_diagram(compact_diagram, "SEM_Compact_Complete")
  
  return(compact_diagram)
}

# ===============================================================================
# Execute Compact Visualization
# ===============================================================================

cat("=== Compact SEM Visualization (Complete Display) ===\n\n")

# Generate the compact diagram
compact_diagram <- generate_compact_diagram()

cat("\n=== Compact Visualization Complete ===\n")
cat("Multiple export formats created to ensure complete visibility!\n\n")

cat("Try these files in order:\n")
cat("1. SEM_Compact_Complete_wide.png (ultra-wide format)\n")
cat("2. SEM_Compact_Complete_16x9.png (widescreen format)\n")
cat("3. SEM_Compact_Complete_4x3.png (standard format)\n")
cat("4. SEM_Compact_Complete_square.png (square format)\n")
cat("5. SEM_Compact_Complete_webshot.png (if webshot available)\n\n")

cat("One of these should display the complete diagram!\n")
