# ===============================================================================
# Example SEM Analysis with Sample Data
# Demonstrates how to run the complete analysis pipeline
# ===============================================================================

# Load required libraries
library(lavaan)
library(semPlot)
library(DiagrammeR)

# ===============================================================================
# 1. GENERATE SAMPLE DATA
# ===============================================================================

generate_sample_data <- function(n = 300) {
  
  set.seed(42)  # For reproducible results
  
  # Generate correlated sample data that reflects the theoretical relationships
  
  # Independent variables
  DC <- rnorm(n, mean = 4.2, sd = 0.8)  # Dynamic Capabilities
  RC <- 0.6 * DC + rnorm(n, mean = 0, sd = 0.7)  # Relational Coordination
  
  # Moderators
  ED <- rnorm(n, mean = 3.8, sd = 0.9)  # Environmental Dynamism
  MCI <- rnorm(n, mean = 3.5, sd = 0.8)  # Market Competition Intensity
  
  # Mediator (influenced by DC and RC, moderated by ED)
  SIC <- 0.4 * DC + 0.3 * RC + 0.2 * DC * ED + 0.15 * RC * ED + rnorm(n, mean = 0, sd = 0.6)
  
  # TBL Performance dimensions (influenced by SIC and direct effects from DC/RC)
  TBL_Economic <- 0.5 * SIC + 0.2 * DC + 0.15 * RC + rnorm(n, mean = 0, sd = 0.5)
  TBL_Social <- 0.45 * SIC + 0.18 * DC + 0.22 * RC + rnorm(n, mean = 0, sd = 0.5)
  TBL_Environmental <- 0.48 * SIC + 0.16 * DC + 0.20 * RC + rnorm(n, mean = 0, sd = 0.5)
  
  # Competitive Advantage (influenced by TBL dimensions, moderated by MCI)
  CA <- 0.3 * TBL_Economic + 0.25 * TBL_Social + 0.28 * TBL_Environmental + 
        0.1 * TBL_Economic * MCI + 0.08 * TBL_Social * MCI + 0.09 * TBL_Environmental * MCI + 
        rnorm(n, mean = 0, sd = 0.4)
  
  # Create interaction terms for lavaan
  DC_ED <- DC * ED
  RC_ED <- RC * ED
  TBL_E_MCI <- TBL_Economic * MCI
  TBL_S_MCI <- TBL_Social * MCI
  TBL_EN_MCI <- TBL_Environmental * MCI
  
  # Combine into data frame
  data <- data.frame(
    DC = DC,
    RC = RC,
    SIC = SIC,
    TBL_Economic = TBL_Economic,
    TBL_Social = TBL_Social,
    TBL_Environmental = TBL_Environmental,
    CA = CA,
    ED = ED,
    MCI = MCI,
    DC_ED = DC_ED,
    RC_ED = RC_ED,
    TBL_E_MCI = TBL_E_MCI,
    TBL_S_MCI = TBL_S_MCI,
    TBL_EN_MCI = TBL_EN_MCI
  )
  
  return(data)
}

# ===============================================================================
# 2. DEFINE SEM MODEL
# ===============================================================================

# Structural model specification
structural_model <- '
  # Direct effects (H1, H2)
  SIC ~ b1*DC + b2*RC
  
  # SIC to TBL dimensions (H3a-c)
  TBL_Economic ~ b3a*SIC
  TBL_Social ~ b3b*SIC
  TBL_Environmental ~ b3c*SIC
  
  # TBL to CA (H4a-c)
  CA ~ b4a*TBL_Economic + b4b*TBL_Social + b4c*TBL_Environmental
  
  # Direct effects (H5a-b)
  TBL_Economic ~ b5a1*DC + b5b1*RC
  TBL_Social ~ b5a2*DC + b5b2*RC
  TBL_Environmental ~ b5a3*DC + b5b3*RC
  
  # Moderation effects (H7)
  SIC ~ b7a*DC_ED + b7b*RC_ED
  CA ~ b7c*TBL_E_MCI + b7d*TBL_S_MCI + b7e*TBL_EN_MCI
  
  # Covariances
  DC ~~ RC
  TBL_Economic ~~ TBL_Social
  TBL_Economic ~~ TBL_Environmental
  TBL_Social ~~ TBL_Environmental
  ED ~~ MCI
  
  # Define indirect effects for mediation analysis (H6)
  # Sequential mediation: DC -> SIC -> TBL_Economic -> CA
  indirect_DC_SIC_TBLE_CA := b1 * b3a * b4a
  indirect_DC_SIC_TBLS_CA := b1 * b3b * b4b
  indirect_DC_SIC_TBLEN_CA := b1 * b3c * b4c
  
  # Sequential mediation: RC -> SIC -> TBL_Economic -> CA
  indirect_RC_SIC_TBLE_CA := b2 * b3a * b4a
  indirect_RC_SIC_TBLS_CA := b2 * b3b * b4b
  indirect_RC_SIC_TBLEN_CA := b2 * b3c * b4c
  
  # Total indirect effects
  total_indirect_DC := indirect_DC_SIC_TBLE_CA + indirect_DC_SIC_TBLS_CA + indirect_DC_SIC_TBLEN_CA
  total_indirect_RC := indirect_RC_SIC_TBLE_CA + indirect_RC_SIC_TBLS_CA + indirect_RC_SIC_TBLEN_CA
'

# ===============================================================================
# 3. RUN ANALYSIS
# ===============================================================================

run_complete_analysis <- function() {
  
  cat("=== RUNNING COMPLETE SEM ANALYSIS ===\n\n")
  
  # Generate sample data
  cat("1. Generating sample data (n=300)...\n")
  data <- generate_sample_data(300)
  
  # Descriptive statistics
  cat("2. Descriptive Statistics:\n")
  desc_stats <- data.frame(
    Variable = names(data)[1:9],  # Exclude interaction terms
    Mean = round(sapply(data[1:9], mean), 3),
    SD = round(sapply(data[1:9], sd), 3),
    Min = round(sapply(data[1:9], min), 3),
    Max = round(sapply(data[1:9], max), 3)
  )
  print(desc_stats)
  
  # Correlation matrix
  cat("\n3. Correlation Matrix:\n")
  cor_matrix <- round(cor(data[1:9]), 3)
  print(cor_matrix)
  
  # Fit the SEM model
  cat("\n4. Fitting SEM model...\n")
  fit <- sem(structural_model, data = data, estimator = "ML")
  
  # Model summary
  cat("\n5. Model Summary:\n")
  summary(fit, fit.measures = TRUE, standardized = TRUE)
  
  # Parameter estimates
  cat("\n6. Parameter Estimates:\n")
  params <- parameterEstimates(fit, standardized = TRUE)
  print(params[params$op == "~", c("lhs", "op", "rhs", "est", "se", "pvalue", "std.all")])
  
  # Indirect effects
  cat("\n7. Indirect Effects (Sequential Mediation):\n")
  indirect <- parameterEstimates(fit)[grep("indirect", parameterEstimates(fit)$label), ]
  print(indirect[, c("label", "est", "se", "pvalue")])
  
  # Model fit indices
  cat("\n8. Model Fit Indices:\n")
  fit_indices <- fitMeasures(fit, c("chisq", "df", "pvalue", "cfi", "tli", "rmsea", "rmsea.ci.lower", "rmsea.ci.upper", "srmr"))
  print(round(fit_indices, 3))
  
  # Save results
  write.csv(desc_stats, "Descriptive_Statistics.csv", row.names = FALSE)
  write.csv(cor_matrix, "Correlation_Matrix.csv")
  write.csv(params, "Parameter_Estimates.csv", row.names = FALSE)
  
  cat("\n=== ANALYSIS COMPLETE ===\n")
  cat("Results saved to CSV files.\n")
  
  return(list(fit = fit, data = data, params = params))
}

# ===============================================================================
# 4. VISUALIZATION WITH RESULTS
# ===============================================================================

create_results_diagram <- function(fit) {
  
  # Extract standardized coefficients
  params <- parameterEstimates(fit, standardized = TRUE)
  
  # Create diagram with actual coefficients
  diagram <- grViz(paste0("
  digraph results_sem {
    
    graph [layout = dot, rankdir = LR, bgcolor = white, fontname = 'Arial']
    node [shape = box, style = filled, fontname = 'Arial', fontsize = 10]
    
    # Nodes
    DC [label = 'Dynamic\\nCapabilities\\n(DC)', fillcolor = lightblue]
    RC [label = 'Relational\\nCoordination\\n(RC)', fillcolor = lightblue]
    SIC [label = 'Service Innovation\\nCapability\\n(SIC)', fillcolor = lightyellow]
    TBL_E [label = 'Economic\\nPerformance', fillcolor = lightgreen]
    TBL_S [label = 'Social\\nPerformance', fillcolor = lightgreen]
    TBL_EN [label = 'Environmental\\nPerformance', fillcolor = lightgreen]
    CA [label = 'Competitive\\nAdvantage\\n(CA)', fillcolor = lightpink]
    
    # Paths with actual coefficients
    DC -> SIC [label = '", round(params[params$lhs == "SIC" & params$rhs == "DC", "std.all"], 3), "***', color = blue, penwidth = 2]
    RC -> SIC [label = '", round(params[params$lhs == "SIC" & params$rhs == "RC", "std.all"], 3), "***', color = blue, penwidth = 2]
    
    SIC -> TBL_E [label = '", round(params[params$lhs == "TBL_Economic" & params$rhs == "SIC", "std.all"], 3), "***', color = green, penwidth = 2]
    SIC -> TBL_S [label = '", round(params[params$lhs == "TBL_Social" & params$rhs == "SIC", "std.all"], 3), "***', color = green, penwidth = 2]
    SIC -> TBL_EN [label = '", round(params[params$lhs == "TBL_Environmental" & params$rhs == "SIC", "std.all"], 3), "***', color = green, penwidth = 2]
    
    TBL_E -> CA [label = '", round(params[params$lhs == "CA" & params$rhs == "TBL_Economic", "std.all"], 3), "***', color = red, penwidth = 2]
    TBL_S -> CA [label = '", round(params[params$lhs == "CA" & params$rhs == "TBL_Social", "std.all"], 3), "***', color = red, penwidth = 2]
    TBL_EN -> CA [label = '", round(params[params$lhs == "CA" & params$rhs == "TBL_Environmental", "std.all"], 3), "***', color = red, penwidth = 2]
  }
  "))
  
  return(diagram)
}

# ===============================================================================
# 5. EXECUTE EXAMPLE ANALYSIS
# ===============================================================================

# Run the complete analysis
cat("Starting example SEM analysis...\n\n")
results <- run_complete_analysis()

# Create results visualization
cat("\nCreating results diagram...\n")
results_diagram <- create_results_diagram(results$fit)
print(results_diagram)

# Additional semPlot visualization
cat("\nCreating semPlot diagram...\n")
semPaths(results$fit, 
         what = "std",
         layout = "tree2",
         style = "lisrel",
         rotation = 2,
         curve = 1,
         nCharNodes = 8,
         sizeMan = 6,
         edge.label.cex = 0.8,
         title = "SEM Results with Standardized Coefficients")

cat("\n=== EXAMPLE ANALYSIS COMPLETE ===\n")
cat("This demonstrates the complete workflow for your SEM analysis.\n")
cat("Replace the sample data with your actual dataset to run the real analysis.\n")
