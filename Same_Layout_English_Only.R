# ===============================================================================
# Same Layout English-Only SEM Visualization
# Keeps the exact same layout but removes Chinese characters
# ===============================================================================

library(DiagrammeR)

# ===============================================================================
# English-Only SEM Diagram with Same Layout
# ===============================================================================

create_same_layout_english_diagram <- function() {
  
  diagram <- grViz("
  digraph same_layout_english {
    
    # Graph settings - same as original
    graph [layout = dot, 
           rankdir = LR, 
           bgcolor = white, 
           fontname = 'Arial',
           fontsize = 12,
           dpi = 300,
           size = '16,10!']
    
    # Node styling - same as original
    node [shape = box, 
          style = 'filled,rounded', 
          fontname = 'Arial', 
          fontsize = 11,
          width = 2.2,
          height = 1.2,
          margin = 0.15]
    
    # Edge styling - same as original
    edge [fontname = 'Arial', 
          fontsize = 10,
          labeldistance = 2.5]
    
    # ===== EXACT SAME RANK STRUCTURE =====
    {rank = same; DC; RC}
    {rank = same; SIC}
    {rank = same; TBL_E; TBL_S; TBL_EN}
    {rank = same; CA}
    {rank = min; ED; MCI}
    
    # ===== NODE DEFINITIONS - ENGLISH ONLY =====
    
    # Independent Variables
    DC [label = 'Dynamic Capabilities\\n(DC)', 
        fillcolor = '#E3F2FD', color = '#1976D2', fontsize = 12]
    
    RC [label = 'Relational Coordination\\n(RC)', 
        fillcolor = '#E8F5E8', color = '#388E3C', fontsize = 12]
    
    # Mediator Variable
    SIC [label = 'Service Innovation\\nCapability\\n(SIC)', 
         fillcolor = '#FFF3E0', color = '#F57C00', fontsize = 12]
    
    # TBL Performance Dimensions
    TBL_E [label = 'Economic Performance\\n(TBL-E)', 
           fillcolor = '#E8F5E8', color = '#4CAF50', fontsize = 11]
    
    TBL_S [label = 'Social Performance\\n(TBL-S)', 
           fillcolor = '#E8F5E8', color = '#4CAF50', fontsize = 11]
    
    TBL_EN [label = 'Environmental Performance\\n(TBL-EN)', 
            fillcolor = '#E8F5E8', color = '#4CAF50', fontsize = 11]
    
    # Dependent Variable
    CA [label = 'Competitive Advantage\\n(CA)', 
        fillcolor = '#FCE4EC', color = '#C2185B', fontsize = 12]
    
    # Moderator Variables
    ED [label = 'Environmental\\nDynamism\\n(ED)', 
        fillcolor = '#F3E5F5', color = '#7B1FA2', shape = ellipse, fontsize = 11]
    
    MCI [label = 'Market Competition\\nIntensity\\n(MCI)', 
         fillcolor = '#F3E5F5', color = '#7B1FA2', shape = ellipse, fontsize = 11]
    
    # ===== EXACT SAME STRUCTURAL RELATIONSHIPS =====
    
    # H1 & H2: Direct antecedent effects
    DC -> SIC [label = 'H1\\nβ₁', color = '#1976D2', penwidth = 3, fontsize = 10, fontcolor = '#1976D2']
    RC -> SIC [label = 'H2\\nβ₂', color = '#388E3C', penwidth = 3, fontsize = 10, fontcolor = '#388E3C']
    
    # H3a-c: SIC to TBL dimensions
    SIC -> TBL_E [label = 'H3a\\nβ₃ₐ', color = '#F57C00', penwidth = 3, fontsize = 10, fontcolor = '#F57C00']
    SIC -> TBL_S [label = 'H3b\\nβ₃ᵦ', color = '#F57C00', penwidth = 3, fontsize = 10, fontcolor = '#F57C00']
    SIC -> TBL_EN [label = 'H3c\\nβ₃ᶜ', color = '#F57C00', penwidth = 3, fontsize = 10, fontcolor = '#F57C00']
    
    # H4a-c: TBL dimensions to CA
    TBL_E -> CA [label = 'H4a\\nβ₄ₐ', color = '#4CAF50', penwidth = 3, fontsize = 10, fontcolor = '#4CAF50']
    TBL_S -> CA [label = 'H4b\\nβ₄ᵦ', color = '#4CAF50', penwidth = 3, fontsize = 10, fontcolor = '#4CAF50']
    TBL_EN -> CA [label = 'H4c\\nβ₄ᶜ', color = '#4CAF50', penwidth = 3, fontsize = 10, fontcolor = '#4CAF50']
    
    # H5a-b: Direct effects (dashed lines)
    DC -> TBL_E [label = 'H5a', color = '#FF5722', style = dashed, penwidth = 2, fontsize = 9, fontcolor = '#FF5722']
    DC -> TBL_S [label = 'H5a', color = '#FF5722', style = dashed, penwidth = 2, fontsize = 9, fontcolor = '#FF5722']
    DC -> TBL_EN [label = 'H5a', color = '#FF5722', style = dashed, penwidth = 2, fontsize = 9, fontcolor = '#FF5722']
    
    RC -> TBL_E [label = 'H5b', color = '#FF5722', style = dashed, penwidth = 2, fontsize = 9, fontcolor = '#FF5722']
    RC -> TBL_S [label = 'H5b', color = '#FF5722', style = dashed, penwidth = 2, fontsize = 9, fontcolor = '#FF5722']
    RC -> TBL_EN [label = 'H5b', color = '#FF5722', style = dashed, penwidth = 2, fontsize = 9, fontcolor = '#FF5722']
    
    # H7: Moderation effects (dotted lines)
    ED -> SIC [label = 'Moderates\\nDC/RC→SIC (H7)', color = '#7B1FA2', style = dotted, penwidth = 2.5, fontsize = 9, fontcolor = '#7B1FA2']
    MCI -> CA [label = 'Moderates\\nTBL→CA (H7)', color = '#7B1FA2', style = dotted, penwidth = 2.5, fontsize = 9, fontcolor = '#7B1FA2']
    
    # Covariances (bidirectional dotted gray lines)
    DC -> RC [dir = both, color = '#9E9E9E', style = dotted, penwidth = 1.5]
    TBL_E -> TBL_S [dir = both, color = '#9E9E9E', style = dotted, penwidth = 1]
    TBL_E -> TBL_EN [dir = both, color = '#9E9E9E', style = dotted, penwidth = 1]
    TBL_S -> TBL_EN [dir = both, color = '#9E9E9E', style = dotted, penwidth = 1]
    
    # Same title
    labelloc = 't'
    label = 'Structural Equation Model: Dynamic Capabilities and Triple Bottom Line Performance'
  }
  ")
  
  return(diagram)
}

# ===============================================================================
# Generate Same Layout English-Only Diagram
# ===============================================================================

generate_same_layout_english <- function() {
  
  cat("Generating English-only SEM diagram with same layout...\n\n")
  
  # Create diagram
  main_diagram <- create_same_layout_english_diagram()
  
  # Display diagram
  cat("Same Layout English-Only SEM Framework:\n")
  print(main_diagram)
  
  # Save as PNG with same large dimensions
  tryCatch({
    if (require(DiagrammeRsvg, quietly = TRUE) && require(rsvg, quietly = TRUE)) {
      
      # Export with extra large dimensions to ensure complete visibility
      main_svg <- DiagrammeRsvg::export_svg(main_diagram)

      # Try multiple sizes to ensure complete diagram
      rsvg::rsvg_png(charToRaw(main_svg), "SEM_Framework_Same_Layout_English_Large.png", width = 4000, height = 2500)
      rsvg::rsvg_png(charToRaw(main_svg), "SEM_Framework_Same_Layout_English_XLarge.png", width = 5000, height = 3000)
      rsvg::rsvg_png(charToRaw(main_svg), "SEM_Framework_Same_Layout_English_Ultra.png", width = 6000, height = 4000)
      
      cat("\n✅ Same layout English-only PNG files saved successfully:\n")
      cat("- SEM_Framework_Same_Layout_English_Large.png (4000x2500)\n")
      cat("- SEM_Framework_Same_Layout_English_XLarge.png (5000x3000)\n")
      cat("- SEM_Framework_Same_Layout_English_Ultra.png (6000x4000)\n\n")
      
      cat("Changes made:\n")
      cat("• Removed all Chinese characters\n")
      cat("• Kept exact same layout and positioning\n")
      cat("• Maintained all colors and styling\n")
      cat("• Preserved all hypothesis labels\n")
      cat("• Same node sizes and spacing\n")
      cat("• Same edge styles and colors\n\n")
      
      cat("The diagram should now display exactly like the original\n")
      cat("but with English-only text labels.\n")
      
    } else {
      cat("DiagrammeRsvg and rsvg packages required for PNG export\n")
    }
    
  }, error = function(e) {
    cat("Error saving PNG files:", e$message, "\n")
  })
  
  return(main_diagram)
}

# ===============================================================================
# Execute Same Layout English-Only Visualization
# ===============================================================================

cat("=== Same Layout English-Only SEM Visualization ===\n\n")

# Generate the diagram
same_layout_diagram <- generate_same_layout_english()

cat("\n=== Same Layout English-Only Complete ===\n")
cat("Perfect! The diagram maintains the exact same layout\n")
cat("as your original but with clean English-only labels.\n\n")

cat("Variable Labels (English Only):\n")
cat("• DC = Dynamic Capabilities\n")
cat("• RC = Relational Coordination\n")
cat("• SIC = Service Innovation Capability\n")
cat("• TBL-E = Economic Performance\n")
cat("• TBL-S = Social Performance\n")
cat("• TBL-EN = Environmental Performance\n")
cat("• CA = Competitive Advantage\n")
cat("• ED = Environmental Dynamism\n")
cat("• MCI = Market Competition Intensity\n\n")

cat("All hypotheses (H1-H7) are clearly labeled and\n")
cat("the sequential mediation pathway is preserved!\n")
