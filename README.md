# Structural Equation Model (SEM) Visualization Package

## Overview

This package provides comprehensive R scripts for creating publication-ready Structural Equation Model (SEM) diagrams based on your theoretical framework examining the relationships between Dynamic Capabilities, Relational Coordination, Service Innovation Capability, Triple Bottom Line Performance, and Competitive Advantage.

## Files Included

### 1. `SEM_Diagram_Generator.R`
- **Purpose**: Main script with complete SEM model specification and basic visualization
- **Features**: 
  - Full lavaan model syntax
  - DiagrammeR visualization
  - Hypothesis table generation
  - Model specification export
  - Basic semPlot integration

### 2. `Advanced_SEM_Visualization.R`
- **Purpose**: Enhanced publication-ready visualization with professional formatting
- **Features**:
  - High-resolution output (300 DPI)
  - Professional typography (Times New Roman)
  - Color-coded relationships
  - Bilingual labels (English/Chinese)
  - Separate legend diagram
  - Optimized layout for academic publications

## Theoretical Framework

### Core Variables
- **Dynamic Capabilities (DC)** - Independent variable (动态能力)
- **Relational Coordination (RC)** - Independent variable (关系协调)
- **Service Innovation Capability (SIC)** - Mediator variable (服务创新能力)
- **Triple Bottom Line Performance (TBL)** with three dimensions:
  - Economic Performance (TBL-Economic) (经济绩效)
  - Social Performance (TBL-Social) (社会绩效)
  - Environmental Performance (TBL-Environmental) (环境绩效)
- **Competitive Advantage (CA)** - Dependent variable (竞争优势)
- **Environmental Dynamism (ED)** - Moderator variable (环境动态性)
- **Market Competition Intensity (MCI)** - Moderator variable (市场竞争强度)

### Research Hypotheses

| Hypothesis | Relationship | Description |
|------------|--------------|-------------|
| H1 | DC → SIC | Dynamic capabilities positively influence service innovation capability |
| H2 | RC → SIC | Relational coordination positively influence service innovation capability |
| H3a | SIC → TBL-Economic | Service innovation capability positively influences economic performance |
| H3b | SIC → TBL-Social | Service innovation capability positively influences social performance |
| H3c | SIC → TBL-Environmental | Service innovation capability positively influences environmental performance |
| H4a | TBL-Economic → CA | Economic performance positively influences competitive advantage |
| H4b | TBL-Social → CA | Social performance positively influences competitive advantage |
| H4c | TBL-Environmental → CA | Environmental performance positively influences competitive advantage |
| H5a | DC → TBL | Dynamic capabilities have direct effects on TBL performance |
| H5b | RC → TBL | Relational coordination has direct effects on TBL performance |
| H6 | DC/RC → SIC → TBL → CA | Sequential mediation through SIC and TBL dimensions |
| H7 | Moderation | ED moderates DC/RC→SIC; MCI moderates TBL→CA |

## Installation and Setup

### Required R Packages

```r
# Install required packages
required_packages <- c("lavaan", "semPlot", "DiagrammeR", "ggplot2", "dplyr", 
                      "gridExtra", "RColorBrewer", "viridis", "htmlwidgets", 
                      "webshot", "rsvg")

install.packages(required_packages)
```

### Additional Setup for High-Resolution Output

```r
# For PNG export functionality
webshot::install_phantomjs()
```

## Usage Instructions

### Quick Start

1. **Basic Visualization**:
```r
source("SEM_Diagram_Generator.R")
# This will generate all basic outputs automatically
```

2. **Advanced Publication-Ready Visualization**:
```r
source("Advanced_SEM_Visualization.R")
# This will create high-resolution diagrams suitable for publication
```

### Customization Options

#### Modifying Colors
```r
# In Advanced_SEM_Visualization.R, modify the fillcolor and color attributes:
DC [fillcolor = '#YOUR_COLOR', color = '#YOUR_BORDER_COLOR']
```

#### Adjusting Layout
```r
# Modify rank assignments to change variable positioning:
{rank = same; DC; RC}  # Variables on same horizontal level
```

#### Changing Font and Size
```r
# Modify graph attributes:
graph [fontname = 'Arial',     # Change font family
       fontsize = 14,          # Change font size
       dpi = 300]              # Change resolution
```

## Output Files

### Generated Files

1. **SEM_Theoretical_Framework.png** - Basic framework diagram
2. **Advanced_SEM_Framework.png** - High-resolution publication-ready diagram
3. **SEM_Legend.png** - Separate legend for the diagram
4. **Research_Hypotheses_Table.csv** - Hypothesis summary table
5. **Full_SEM_Model.txt** - Complete lavaan model syntax
6. **Simple_SEM_Model.txt** - Simplified model for visualization

### File Specifications

- **Resolution**: 300 DPI for publication quality
- **Format**: PNG with transparent background option
- **Dimensions**: Optimized for academic papers (1400x900 for main diagram)

## Diagram Legend

### Path Types
- **Solid Blue Lines**: Direct antecedent relationships (H1, H2)
- **Solid Orange Lines**: Mediation paths through SIC (H3a-c)
- **Solid Green Lines**: TBL to competitive advantage (H4a-c)
- **Dashed Red Lines**: Direct effects bypassing SIC (H5a-b)
- **Dotted Purple Lines**: Moderation effects (H7)
- **Dotted Gray Lines**: Covariances between variables

### Variable Types
- **Blue Boxes**: Independent Variables
- **Orange Box**: Mediator Variable
- **Green Boxes**: TBL Performance Dimensions
- **Pink Box**: Dependent Variable
- **Purple Ellipses**: Moderator Variables

## Empirical Analysis

### Using Your Own Data

Replace the sample data generation section with your actual dataset:

```r
# Replace this section in SEM_Diagram_Generator.R
sample_data <- read.csv("your_data.csv")

# Fit the model with your data
fit <- sem(simple_model, data = sample_data, estimator = "ML")
```

### Model Fit Assessment

```r
# Check model fit
summary(fit, fit.measures = TRUE, standardized = TRUE)

# Additional fit indices
fitMeasures(fit, c("chisq", "df", "pvalue", "cfi", "tli", "rmsea", "srmr"))
```

## Troubleshooting

### Common Issues

1. **Package Installation Errors**:
   - Ensure R version is 4.0 or higher
   - Install packages one by one if batch installation fails

2. **PNG Export Issues**:
   - Install phantomjs: `webshot::install_phantomjs()`
   - Check that rsvg package is properly installed

3. **Font Issues**:
   - Times New Roman may not be available on all systems
   - Replace with "Arial" or "Helvetica" if needed

4. **Layout Problems**:
   - Adjust rank assignments in the DiagrammeR code
   - Modify node dimensions (width, height) for better spacing

## Citation

If you use this visualization package in your research, please cite appropriately and acknowledge the theoretical framework development.

## Support

For questions or issues with the code, please refer to the comments within each script or consult the R package documentation for lavaan, semPlot, and DiagrammeR.

---

**Note**: This package is designed for academic research purposes. Ensure that your theoretical model and hypotheses are properly validated before using these visualizations in publications.
