# ===============================================================================
# Simple SEM Visualization - Direct PNG Generation
# Publication-Ready Structural Equation Model Diagram
# ===============================================================================

library(DiagrammeR)

# ===============================================================================
# SIMPLE SEM DIAGRAM GENERATOR
# ===============================================================================

create_simple_sem_diagram <- function() {
  
  diagram <- grViz("
  digraph simple_sem {
    
    # Graph settings
    graph [layout = dot, 
           rankdir = LR, 
           bgcolor = white, 
           fontname = 'Arial',
           fontsize = 12]
    
    # Node styling
    node [shape = box, 
          style = 'filled,rounded', 
          fontname = 'Arial', 
          fontsize = 10,
          width = 1.8,
          height = 1.0]
    
    # Edge styling  
    edge [fontname = 'Arial', 
          fontsize = 9]
    
    # ===== RANK STRUCTURE =====
    {rank = same; DC; RC}
    {rank = same; SIC}
    {rank = same; TBL_E; TBL_S; TBL_EN}
    {rank = same; CA}
    {rank = min; ED; MCI}
    
    # ===== NODE DEFINITIONS =====
    
    # Independent Variables
    DC [label = 'Dynamic Capabilities\\n(DC)\\n动态能力', 
        fillcolor = '#E3F2FD', color = '#1976D2']
    
    RC [label = 'Relational Coordination\\n(RC)\\n关系协调', 
        fillcolor = '#E8F5E8', color = '#388E3C']
    
    # Mediator
    SIC [label = 'Service Innovation\\nCapability (SIC)\\n服务创新能力', 
         fillcolor = '#FFF3E0', color = '#F57C00']
    
    # TBL Performance Dimensions
    TBL_E [label = 'Economic Performance\\n(TBL-E)\\n经济绩效', 
           fillcolor = '#E8F5E8', color = '#4CAF50']
    
    TBL_S [label = 'Social Performance\\n(TBL-S)\\n社会绩效', 
           fillcolor = '#E8F5E8', color = '#4CAF50']
    
    TBL_EN [label = 'Environmental Performance\\n(TBL-EN)\\n环境绩效', 
            fillcolor = '#E8F5E8', color = '#4CAF50']
    
    # Dependent Variable
    CA [label = 'Competitive Advantage\\n(CA)\\n竞争优势', 
        fillcolor = '#FCE4EC', color = '#C2185B']
    
    # Moderators
    ED [label = 'Environmental Dynamism\\n(ED)\\n环境动态性', 
        fillcolor = '#F3E5F5', color = '#7B1FA2', shape = ellipse]
    
    MCI [label = 'Market Competition\\nIntensity (MCI)\\n市场竞争强度', 
         fillcolor = '#F3E5F5', color = '#7B1FA2', shape = ellipse]
    
    # ===== STRUCTURAL RELATIONSHIPS =====
    
    # H1 & H2: Direct antecedents to SIC
    DC -> SIC [label = 'β₁ (H1)', color = '#1976D2', penwidth = 2.5]
    RC -> SIC [label = 'β₂ (H2)', color = '#388E3C', penwidth = 2.5]
    
    # H3a-c: SIC to TBL dimensions
    SIC -> TBL_E [label = 'β₃ₐ (H3a)', color = '#F57C00', penwidth = 2.5]
    SIC -> TBL_S [label = 'β₃ᵦ (H3b)', color = '#F57C00', penwidth = 2.5]
    SIC -> TBL_EN [label = 'β₃ᶜ (H3c)', color = '#F57C00', penwidth = 2.5]
    
    # H4a-c: TBL dimensions to CA
    TBL_E -> CA [label = 'β₄ₐ (H4a)', color = '#4CAF50', penwidth = 2.5]
    TBL_S -> CA [label = 'β₄ᵦ (H4b)', color = '#4CAF50', penwidth = 2.5]
    TBL_EN -> CA [label = 'β₄ᶜ (H4c)', color = '#4CAF50', penwidth = 2.5]
    
    # H5a-b: Direct effects (dashed lines)
    DC -> TBL_E [label = 'β₅ₐ₁ (H5a)', color = '#FF5722', style = dashed, penwidth = 1.5]
    DC -> TBL_S [label = 'β₅ₐ₂ (H5a)', color = '#FF5722', style = dashed, penwidth = 1.5]
    DC -> TBL_EN [label = 'β₅ₐ₃ (H5a)', color = '#FF5722', style = dashed, penwidth = 1.5]
    
    RC -> TBL_E [label = 'β₅ᵦ₁ (H5b)', color = '#FF5722', style = dashed, penwidth = 1.5]
    RC -> TBL_S [label = 'β₅ᵦ₂ (H5b)', color = '#FF5722', style = dashed, penwidth = 1.5]
    RC -> TBL_EN [label = 'β₅ᵦ₃ (H5b)', color = '#FF5722', style = dashed, penwidth = 1.5]
    
    # H7: Moderation effects (dotted lines)
    ED -> SIC [label = 'Moderates\\nDC/RC→SIC (H7)', color = '#7B1FA2', style = dotted, penwidth = 2]
    MCI -> CA [label = 'Moderates\\nTBL→CA (H7)', color = '#7B1FA2', style = dotted, penwidth = 2]
    
    # Covariances (bidirectional dotted gray lines)
    DC -> RC [dir = both, color = '#9E9E9E', style = dotted, penwidth = 1]
    TBL_E -> TBL_S [dir = both, color = '#9E9E9E', style = dotted, penwidth = 1]
    TBL_E -> TBL_EN [dir = both, color = '#9E9E9E', style = dotted, penwidth = 1]
    TBL_S -> TBL_EN [dir = both, color = '#9E9E9E', style = dotted, penwidth = 1]
    
    # Title
    labelloc = 't'
    label = 'Structural Equation Model: Dynamic Capabilities and Triple Bottom Line Performance'
  }
  ")
  
  return(diagram)
}

# ===============================================================================
# CREATE LEGEND
# ===============================================================================

create_simple_legend <- function() {
  
  legend <- grViz("
  digraph legend {
    
    graph [layout = dot, rankdir = TB, bgcolor = white, fontname = 'Arial']
    node [shape = plaintext, fontname = 'Arial', fontsize = 11]
    
    title [label = 'Legend - Path Types and Variables']
    
    # Path types
    p1 [label = '━━━ Direct Antecedent Effects (H1, H2) - Blue/Green']
    p2 [label = '━━━ Mediation through SIC (H3a-c) - Orange']
    p3 [label = '━━━ TBL to Competitive Advantage (H4a-c) - Green']
    p4 [label = '┅┅┅ Direct Effects (H5a-b) - Red Dashed']
    p5 [label = '⋯⋯⋯ Moderation Effects (H7) - Purple Dotted']
    p6 [label = '⋯⋯⋯ Covariances - Gray Dotted']
    
    # Variable types
    v1 [label = 'Blue: Independent Variables (DC, RC)']
    v2 [label = 'Orange: Mediator Variable (SIC)']
    v3 [label = 'Green: TBL Performance Dimensions']
    v4 [label = 'Pink: Dependent Variable (CA)']
    v5 [label = 'Purple Ellipses: Moderator Variables (ED, MCI)']
    
    title -> p1 -> p2 -> p3 -> p4 -> p5 -> p6 -> v1 -> v2 -> v3 -> v4 -> v5
  }
  ")
  
  return(legend)
}

# ===============================================================================
# GENERATE AND SAVE DIAGRAMS
# ===============================================================================

generate_simple_diagrams <- function() {
  
  cat("Generating simple SEM diagrams...\n\n")
  
  # Create diagrams
  main_diagram <- create_simple_sem_diagram()
  legend_diagram <- create_simple_legend()
  
  # Display diagrams
  cat("Main SEM Framework:\n")
  print(main_diagram)
  
  cat("\nLegend:\n")
  print(legend_diagram)
  
  # Try to save as PNG using different methods
  tryCatch({
    # Method 1: Using DiagrammeR's export function
    if (require(DiagrammeRsvg, quietly = TRUE) && require(rsvg, quietly = TRUE)) {
      
      # Export main diagram
      main_svg <- DiagrammeRsvg::export_svg(main_diagram)
      rsvg::rsvg_png(charToRaw(main_svg), "SEM_Framework_Main.png", width = 1200, height = 800)
      
      # Export legend
      legend_svg <- DiagrammeRsvg::export_svg(legend_diagram)
      rsvg::rsvg_png(charToRaw(legend_svg), "SEM_Framework_Legend.png", width = 600, height = 800)
      
      cat("\nPNG files saved successfully:\n")
      cat("- SEM_Framework_Main.png\n")
      cat("- SEM_Framework_Legend.png\n")
      
    } else {
      cat("\nDiagrammeRsvg or rsvg package not available.\n")
      cat("Diagrams are displayed above but not saved as PNG files.\n")
      cat("You can manually save them from the R viewer or install the required packages.\n")
    }
    
  }, error = function(e) {
    cat("\nError saving PNG files:", e$message, "\n")
    cat("Diagrams are displayed above but not saved as PNG files.\n")
  })
  
  return(list(main = main_diagram, legend = legend_diagram))
}

# ===============================================================================
# EXECUTE SIMPLE VISUALIZATION
# ===============================================================================

# Generate the diagrams
cat("=== Simple SEM Visualization ===\n")
diagrams <- generate_simple_diagrams()

cat("\n=== Visualization Complete ===\n")
cat("Features:\n")
cat("• Clear theoretical framework visualization\n")
cat("• All hypotheses (H1-H7) represented\n")
cat("• Sequential mediation pathway shown\n")
cat("• Moderation effects marked\n")
cat("• Bilingual labels (English/Chinese)\n")
cat("• Color-coded relationships\n")
cat("• Professional layout\n\n")

cat("Variable Abbreviations:\n")
cat("DC = Dynamic Capabilities (动态能力)\n")
cat("RC = Relational Coordination (关系协调)\n")
cat("SIC = Service Innovation Capability (服务创新能力)\n")
cat("TBL-E = Economic Performance (经济绩效)\n")
cat("TBL-S = Social Performance (社会绩效)\n")
cat("TBL-EN = Environmental Performance (环境绩效)\n")
cat("CA = Competitive Advantage (竞争优势)\n")
cat("ED = Environmental Dynamism (环境动态性)\n")
cat("MCI = Market Competition Intensity (市场竞争强度)\n")
