# ===============================================================================
# Install Required Packages for SEM Visualization
# ===============================================================================

cat("Installing required R packages for SEM visualization...\n\n")

# List of required packages
required_packages <- c(
  "DiagrammeR",
  "htmlwidgets", 
  "webshot",
  "rsvg",
  "lavaan",
  "semPlot",
  "ggplot2",
  "dplyr"
)

# Function to install packages if not already installed
install_if_missing <- function(packages) {
  for (pkg in packages) {
    if (!require(pkg, character.only = TRUE, quietly = TRUE)) {
      cat("Installing package:", pkg, "\n")
      install.packages(pkg, repos = "https://cran.r-project.org/")
    } else {
      cat("Package", pkg, "is already installed.\n")
    }
  }
}

# Install packages
install_if_missing(required_packages)

# Install phantomjs for webshot (needed for PNG export)
cat("\nInstalling phantomjs for PNG export...\n")
if (require(webshot, quietly = TRUE)) {
  webshot::install_phantomjs()
  cat("phantomjs installed successfully.\n")
} else {
  cat("webshot package not available. PNG export may not work.\n")
}

cat("\n=== Package Installation Complete ===\n")
cat("All required packages have been installed.\n")
cat("You can now run the SEM visualization scripts.\n")
