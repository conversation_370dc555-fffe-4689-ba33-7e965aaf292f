# ===============================================================================
# English-Only SEM Visualization
# Clean Professional Structural Equation Model Diagram
# ===============================================================================

library(DiagrammeR)

# ===============================================================================
# English-Only SEM Diagram Generator
# ===============================================================================

create_english_sem_diagram <- function() {
  
  diagram <- grViz("
  digraph english_sem {
    
    # Graph settings for high quality output
    graph [layout = dot, 
           rankdir = LR, 
           bgcolor = white, 
           fontname = 'Arial',
           fontsize = 14,
           dpi = 300,
           size = '16,10!']
    
    # Node styling - optimized for English text
    node [shape = box, 
          style = 'filled,rounded', 
          fontname = 'Arial', 
          fontsize = 12,
          width = 2.0,
          height = 1.0,
          margin = 0.1]
    
    # Edge styling
    edge [fontname = 'Arial', 
          fontsize = 10,
          labeldistance = 2.0]
    
    # ===== RANK STRUCTURE =====
    {rank = same; DC; RC}
    {rank = same; SIC}
    {rank = same; TBL_E; TBL_S; TBL_EN}
    {rank = same; CA}
    {rank = min; ED; MCI}
    
    # ===== NODE DEFINITIONS =====
    
    # Independent Variables
    DC [label = 'Dynamic\\nCapabilities\\n(DC)', 
        fillcolor = '#E3F2FD', color = '#1976D2', fontsize = 12]
    
    RC [label = 'Relational\\nCoordination\\n(RC)', 
        fillcolor = '#E8F5E8', color = '#388E3C', fontsize = 12]
    
    # Mediator Variable
    SIC [label = 'Service Innovation\\nCapability\\n(SIC)', 
         fillcolor = '#FFF3E0', color = '#F57C00', fontsize = 12]
    
    # TBL Performance Dimensions
    TBL_E [label = 'Economic\\nPerformance\\n(TBL-E)', 
           fillcolor = '#E8F5E8', color = '#4CAF50', fontsize = 11]
    
    TBL_S [label = 'Social\\nPerformance\\n(TBL-S)', 
           fillcolor = '#E8F5E8', color = '#4CAF50', fontsize = 11]
    
    TBL_EN [label = 'Environmental\\nPerformance\\n(TBL-EN)', 
            fillcolor = '#E8F5E8', color = '#4CAF50', fontsize = 11]
    
    # Dependent Variable
    CA [label = 'Competitive\\nAdvantage\\n(CA)', 
        fillcolor = '#FCE4EC', color = '#C2185B', fontsize = 12]
    
    # Moderator Variables
    ED [label = 'Environmental\\nDynamism\\n(ED)', 
        fillcolor = '#F3E5F5', color = '#7B1FA2', shape = ellipse, fontsize = 11]
    
    MCI [label = 'Market Competition\\nIntensity\\n(MCI)', 
         fillcolor = '#F3E5F5', color = '#7B1FA2', shape = ellipse, fontsize = 11]
    
    # ===== STRUCTURAL RELATIONSHIPS =====
    
    # H1 & H2: Direct antecedent effects
    DC -> SIC [label = 'H1\\nβ₁', color = '#1976D2', penwidth = 3, fontsize = 10, fontcolor = '#1976D2']
    RC -> SIC [label = 'H2\\nβ₂', color = '#388E3C', penwidth = 3, fontsize = 10, fontcolor = '#388E3C']
    
    # H3a-c: SIC to TBL dimensions
    SIC -> TBL_E [label = 'H3a\\nβ₃ₐ', color = '#F57C00', penwidth = 3, fontsize = 10, fontcolor = '#F57C00']
    SIC -> TBL_S [label = 'H3b\\nβ₃ᵦ', color = '#F57C00', penwidth = 3, fontsize = 10, fontcolor = '#F57C00']
    SIC -> TBL_EN [label = 'H3c\\nβ₃ᶜ', color = '#F57C00', penwidth = 3, fontsize = 10, fontcolor = '#F57C00']
    
    # H4a-c: TBL dimensions to CA
    TBL_E -> CA [label = 'H4a\\nβ₄ₐ', color = '#4CAF50', penwidth = 3, fontsize = 10, fontcolor = '#4CAF50']
    TBL_S -> CA [label = 'H4b\\nβ₄ᵦ', color = '#4CAF50', penwidth = 3, fontsize = 10, fontcolor = '#4CAF50']
    TBL_EN -> CA [label = 'H4c\\nβ₄ᶜ', color = '#4CAF50', penwidth = 3, fontsize = 10, fontcolor = '#4CAF50']
    
    # H5a-b: Direct effects (dashed lines)
    DC -> TBL_E [label = 'H5a', color = '#FF5722', style = dashed, penwidth = 2, fontsize = 9, fontcolor = '#FF5722']
    DC -> TBL_S [label = 'H5a', color = '#FF5722', style = dashed, penwidth = 2, fontsize = 9, fontcolor = '#FF5722']
    DC -> TBL_EN [label = 'H5a', color = '#FF5722', style = dashed, penwidth = 2, fontsize = 9, fontcolor = '#FF5722']
    
    RC -> TBL_E [label = 'H5b', color = '#FF5722', style = dashed, penwidth = 2, fontsize = 9, fontcolor = '#FF5722']
    RC -> TBL_S [label = 'H5b', color = '#FF5722', style = dashed, penwidth = 2, fontsize = 9, fontcolor = '#FF5722']
    RC -> TBL_EN [label = 'H5b', color = '#FF5722', style = dashed, penwidth = 2, fontsize = 9, fontcolor = '#FF5722']
    
    # H7: Moderation effects (dotted lines)
    ED -> SIC [label = 'H7\\nModerates\\nDC/RC→SIC', color = '#7B1FA2', style = dotted, penwidth = 2.5, fontsize = 9, fontcolor = '#7B1FA2']
    MCI -> CA [label = 'H7\\nModerates\\nTBL→CA', color = '#7B1FA2', style = dotted, penwidth = 2.5, fontsize = 9, fontcolor = '#7B1FA2']
    
    # Covariances (bidirectional dotted lines)
    DC -> RC [dir = both, color = '#9E9E9E', style = dotted, penwidth = 1.5]
    TBL_E -> TBL_S [dir = both, color = '#9E9E9E', style = dotted, penwidth = 1]
    TBL_E -> TBL_EN [dir = both, color = '#9E9E9E', style = dotted, penwidth = 1]
    TBL_S -> TBL_EN [dir = both, color = '#9E9E9E', style = dotted, penwidth = 1]
    
    # Title
    labelloc = 't'
    label = 'Structural Equation Model: Dynamic Capabilities and Triple Bottom Line Performance'
  }
  ")
  
  return(diagram)
}

# ===============================================================================
# English-Only Legend
# ===============================================================================

create_english_legend <- function() {
  
  legend <- grViz("
  digraph english_legend {
    
    graph [layout = dot, rankdir = TB, bgcolor = white, fontname = 'Arial', fontsize = 12]
    node [shape = plaintext, fontname = 'Arial', fontsize = 12]
    
    title [label = 'Legend\\n\\nPath Types:', fontsize = 14, fontcolor = 'black']
    
    # Path types
    p1 [label = '━━━ Direct Antecedent Effects (H1, H2)', fontcolor = '#1976D2']
    p2 [label = '━━━ Mediation through SIC (H3a-c)', fontcolor = '#F57C00']
    p3 [label = '━━━ TBL to Competitive Advantage (H4a-c)', fontcolor = '#4CAF50']
    p4 [label = '┅┅┅ Direct Effects (H5a-b)', fontcolor = '#FF5722']
    p5 [label = '⋯⋯⋯ Moderation Effects (H7)', fontcolor = '#7B1FA2']
    p6 [label = '⋯⋯⋯ Covariances', fontcolor = '#9E9E9E']
    
    blank1 [label = '\\nVariable Types:', fontsize = 14, fontcolor = 'black']
    
    # Variable types
    v1 [label = '■ Blue Boxes: Independent Variables (DC, RC)', fontcolor = '#1976D2']
    v2 [label = '■ Orange Box: Mediator Variable (SIC)', fontcolor = '#F57C00']
    v3 [label = '■ Green Boxes: TBL Performance Dimensions', fontcolor = '#4CAF50']
    v4 [label = '■ Pink Box: Dependent Variable (CA)', fontcolor = '#C2185B']
    v5 [label = '● Purple Ellipses: Moderator Variables (ED, MCI)', fontcolor = '#7B1FA2']
    
    blank2 [label = '\\nHypotheses:', fontsize = 14, fontcolor = 'black']
    
    # Hypotheses
    h1 [label = 'H1: DC → SIC (Dynamic Capabilities → Service Innovation Capability)']
    h2 [label = 'H2: RC → SIC (Relational Coordination → Service Innovation Capability)']
    h3 [label = 'H3: SIC → TBL (Service Innovation Capability → Triple Bottom Line)']
    h4 [label = 'H4: TBL → CA (Triple Bottom Line → Competitive Advantage)']
    h5 [label = 'H5: DC/RC → TBL (Direct Effects)']
    h6 [label = 'H6: DC/RC → SIC → TBL → CA (Sequential Mediation)']
    h7 [label = 'H7: Moderation (ED moderates DC/RC→SIC; MCI moderates TBL→CA)']
    
    # Layout
    title -> p1 -> p2 -> p3 -> p4 -> p5 -> p6 -> blank1 -> v1 -> v2 -> v3 -> v4 -> v5 -> blank2 -> h1 -> h2 -> h3 -> h4 -> h5 -> h6 -> h7
  }
  ")
  
  return(legend)
}

# ===============================================================================
# Generate English-Only Diagrams
# ===============================================================================

generate_english_diagrams <- function() {
  
  cat("Generating English-only SEM diagrams...\n\n")
  
  # Create diagrams
  main_diagram <- create_english_sem_diagram()
  legend_diagram <- create_english_legend()
  
  # Display diagrams
  cat("Main SEM Framework:\n")
  print(main_diagram)
  
  cat("\nLegend:\n")
  print(legend_diagram)
  
  # Save as PNG
  tryCatch({
    if (require(DiagrammeRsvg, quietly = TRUE) && require(rsvg, quietly = TRUE)) {
      
      # Export main diagram - high resolution
      main_svg <- DiagrammeRsvg::export_svg(main_diagram)
      rsvg::rsvg_png(charToRaw(main_svg), "SEM_Framework_English_Main.png", width = 1600, height = 1000)
      
      # Export legend - high resolution
      legend_svg <- DiagrammeRsvg::export_svg(legend_diagram)
      rsvg::rsvg_png(charToRaw(legend_svg), "SEM_Framework_English_Legend.png", width = 800, height = 1200)
      
      cat("\n✅ English-only PNG files saved successfully:\n")
      cat("- SEM_Framework_English_Main.png (1600x1000)\n")
      cat("- SEM_Framework_English_Legend.png (800x1200)\n\n")
      
      cat("Features:\n")
      cat("• Clean English-only labels\n")
      cat("• Professional typography (Arial font)\n")
      cat("• High resolution (1600x1000)\n")
      cat("• Clear hypothesis labeling\n")
      cat("• Color-coded relationships\n")
      cat("• Publication-ready quality\n")
      
    } else {
      cat("DiagrammeRsvg and rsvg packages required for PNG export\n")
    }
    
  }, error = function(e) {
    cat("Error saving PNG files:", e$message, "\n")
  })
  
  return(list(main = main_diagram, legend = legend_diagram))
}

# ===============================================================================
# Execute English-Only Visualization
# ===============================================================================

cat("=== English-Only SEM Visualization ===\n\n")

# Generate the diagrams
english_diagrams <- generate_english_diagrams()

cat("\n=== Visualization Complete ===\n")
cat("Clean English-only SEM diagrams generated!\n\n")

cat("Variable Abbreviations:\n")
cat("DC = Dynamic Capabilities\n")
cat("RC = Relational Coordination\n")
cat("SIC = Service Innovation Capability\n")
cat("TBL-E = Economic Performance\n")
cat("TBL-S = Social Performance\n")
cat("TBL-EN = Environmental Performance\n")
cat("CA = Competitive Advantage\n")
cat("ED = Environmental Dynamism\n")
cat("MCI = Market Competition Intensity\n\n")

cat("Sequential Mediation Chain (H6):\n")
cat("DC/RC → SIC → TBL → CA\n\n")

cat("The diagrams are now ready for academic publication!\n")
