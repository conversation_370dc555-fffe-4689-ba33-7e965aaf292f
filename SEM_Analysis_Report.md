# 结构方程模型 (SEM) 分析报告
# Structural Equation Model Analysis Report

## 📊 生成的文件 / Generated Files

### 🖼️ 可视化图表 / Visualization Charts
1. **SEM_Framework_Main.png** - 主要理论框架图 (1200x800像素)
2. **SEM_Framework_Legend.png** - 图例说明 (600x800像素)

### 📈 数据分析结果 / Data Analysis Results  
3. **Descriptive_Statistics.csv** - 描述性统计
4. **Correlation_Matrix.csv** - 相关性矩阵
5. **Parameter_Estimates.csv** - 参数估计结果

### 💻 R脚本文件 / R Script Files
6. **Simple_SEM_Visualization.R** - 简化版可视化脚本 (推荐使用)
7. **Advanced_SEM_Visualization.R** - 高级可视化脚本
8. **SEM_Diagram_Generator.R** - 基础图表生成器
9. **Example_Analysis.R** - 示例分析脚本
10. **install_packages.R** - 包安装脚本

## 🎯 理论框架概述 / Theoretical Framework Overview

### 核心变量 / Core Variables
- **动态能力 (DC)** - Dynamic Capabilities - 自变量
- **关系协调 (RC)** - Relational Coordination - 自变量  
- **服务创新能力 (SIC)** - Service Innovation Capability - 中介变量
- **三重底线绩效 (TBL)** - Triple Bottom Line Performance:
  - 经济绩效 (TBL-E) - Economic Performance
  - 社会绩效 (TBL-S) - Social Performance  
  - 环境绩效 (TBL-EN) - Environmental Performance
- **竞争优势 (CA)** - Competitive Advantage - 因变量
- **环境动态性 (ED)** - Environmental Dynamism - 调节变量
- **市场竞争强度 (MCI)** - Market Competition Intensity - 调节变量

### 研究假设 / Research Hypotheses

| 假设 | 关系 | 描述 |
|------|------|------|
| H1 | DC → SIC | 动态能力正向影响服务创新能力 |
| H2 | RC → SIC | 关系协调正向影响服务创新能力 |
| H3a | SIC → TBL-E | 服务创新能力正向影响经济绩效 |
| H3b | SIC → TBL-S | 服务创新能力正向影响社会绩效 |
| H3c | SIC → TBL-EN | 服务创新能力正向影响环境绩效 |
| H4a | TBL-E → CA | 经济绩效正向影响竞争优势 |
| H4b | TBL-S → CA | 社会绩效正向影响竞争优势 |
| H4c | TBL-EN → CA | 环境绩效正向影响竞争优势 |
| H5a | DC → TBL | 动态能力对三重底线绩效有直接影响 |
| H5b | RC → TBL | 关系协调对三重底线绩效有直接影响 |
| H6 | DC/RC → SIC → TBL → CA | 通过SIC和TBL的序列中介效应 |
| H7 | 调节效应 | ED调节DC/RC→SIC; MCI调节TBL→CA |

## 📊 示例分析结果 / Sample Analysis Results

### 描述性统计 / Descriptive Statistics
| 变量 | 均值 | 标准差 | 最小值 | 最大值 |
|------|------|--------|--------|--------|
| DC | 4.183 | 0.790 | 1.806 | 6.362 |
| RC | 2.490 | 0.849 | 0.483 | 4.986 |
| SIC | 6.904 | 1.789 | 2.135 | 12.006 |
| TBL-E | 4.690 | 1.202 | 0.851 | 7.720 |
| TBL-S | 4.399 | 1.193 | 1.335 | 7.532 |
| TBL-EN | 4.495 | 1.160 | 1.554 | 7.878 |
| CA | 8.082 | 2.290 | 1.980 | 16.609 |

### 关键路径系数 / Key Path Coefficients
| 路径 | 标准化系数 | 显著性 |
|------|------------|--------|
| DC → SIC | 0.169 | *** |
| RC → SIC | 0.240 | *** |
| SIC → TBL-E | 0.756 | *** |
| SIC → TBL-S | 0.748 | *** |
| SIC → TBL-EN | 0.735 | *** |
| TBL-S → CA | 0.189 | *** |
| TBL-EN → CA | 0.187 | *** |

### 间接效应 / Indirect Effects
| 路径 | 效应值 | 显著性 |
|------|--------|--------|
| DC总间接效应 | 0.130 | *** |
| RC总间接效应 | 0.172 | *** |

## 🎨 图表说明 / Chart Legend

### 路径类型 / Path Types
- **蓝色实线** - 直接前因效应 (H1, H2)
- **橙色实线** - 通过SIC的中介效应 (H3a-c)
- **绿色实线** - TBL到竞争优势 (H4a-c)
- **红色虚线** - 直接效应 (H5a-b)
- **紫色点线** - 调节效应 (H7)
- **灰色点线** - 协方差

### 变量类型 / Variable Types
- **蓝色方框** - 自变量 (DC, RC)
- **橙色方框** - 中介变量 (SIC)
- **绿色方框** - TBL绩效维度
- **粉色方框** - 因变量 (CA)
- **紫色椭圆** - 调节变量 (ED, MCI)

## 🚀 使用说明 / Usage Instructions

### 快速开始 / Quick Start
1. 运行 `Simple_SEM_Visualization.R` 生成基础图表
2. 查看生成的PNG文件
3. 根据需要修改颜色和布局

### 自定义分析 / Custom Analysis
1. 将示例数据替换为您的实际数据
2. 运行 `Example_Analysis.R` 进行完整分析
3. 检查模型拟合度和参数显著性

### 发表准备 / Publication Ready
- PNG文件适合插入学术论文
- 包含中英文标签
- 专业的颜色编码和布局
- 清晰的假设标识

## ✅ 成功完成的任务 / Successfully Completed Tasks

✅ **理论框架可视化** - 完整的SEM图表，包含所有假设关系  
✅ **序列中介模型** - 清晰展示DC/RC → SIC → TBL → CA路径  
✅ **调节效应** - ED和MCI的调节作用可视化  
✅ **双语标签** - 中英文变量名称  
✅ **专业格式** - 适合学术发表的高质量图表  
✅ **完整分析** - 包含描述性统计、相关性和参数估计  
✅ **PNG输出** - 高分辨率图像文件  

## 📝 注意事项 / Notes

1. **模型拟合度**: 示例数据的拟合度较低，实际数据可能会有更好的拟合
2. **样本量**: 建议使用至少200-300个样本进行SEM分析
3. **假设检验**: 根据实际数据结果调整理论模型
4. **软件要求**: 需要R 4.0+和相关包

---

**生成时间**: 2025年1月14日  
**软件版本**: R 4.5.0, lavaan 0.6-19, DiagrammeR  
**状态**: ✅ 分析完成，图表已生成
