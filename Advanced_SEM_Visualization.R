# ===============================================================================
# Advanced SEM Visualization with Enhanced Layout Control
# Publication-Ready Structural Equation Model Diagram
# ===============================================================================

library(DiagrammeR)
library(htmlwidgets)
library(webshot)

# ===============================================================================
# ADVANCED SEM DIAGRAM WITH PRECISE POSITIONING
# ===============================================================================

create_advanced_sem_diagram <- function() {
  
  diagram <- grViz("
  digraph advanced_sem {
    
    # Graph settings for publication quality
    graph [layout = dot, 
           rankdir = LR, 
           bgcolor = white, 
           fontname = 'Times New Roman',
           fontsize = 12,
           dpi = 300,
           size = '12,8!',
           ratio = fill]
    
    # Node styling
    node [shape = box, 
          style = 'filled,rounded', 
          fontname = 'Times New Roman', 
          fontsize = 11,
          width = 1.5,
          height = 0.8,
          margin = 0.1]
    
    # Edge styling  
    edge [fontname = 'Times New Roman', 
          fontsize = 9,
          labeldistance = 2,
          labelangle = 0]
    
    # ===== RANK STRUCTURE FOR PROPER LAYOUT =====
    
    # Rank 1: Independent Variables
    {rank = same; DC; RC}
    
    # Rank 2: Mediator
    {rank = same; SIC}
    
    # Rank 3: TBL Performance Dimensions  
    {rank = same; TBL_E; TBL_S; TBL_EN}
    
    # Rank 4: Dependent Variable
    {rank = same; CA}
    
    # Rank 5: Moderators (positioned separately)
    {rank = min; ED; MCI}
    
    # ===== NODE DEFINITIONS =====
    
    # Independent Variables
    DC [label = <<B>Dynamic Capabilities</B><BR/><FONT POINT-SIZE='9'>(DC)</FONT><BR/><FONT POINT-SIZE='8'>动态能力</FONT>>, 
        fillcolor = '#E3F2FD', color = '#1976D2']
    
    RC [label = <<B>Relational Coordination</B><BR/><FONT POINT-SIZE='9'>(RC)</FONT><BR/><FONT POINT-SIZE='8'>关系协调</FONT>>, 
        fillcolor = '#E8F5E8', color = '#388E3C']
    
    # Mediator
    SIC [label = <<B>Service Innovation</B><BR/><B>Capability</B><BR/><FONT POINT-SIZE='9'>(SIC)</FONT><BR/><FONT POINT-SIZE='8'>服务创新能力</FONT>>, 
         fillcolor = '#FFF3E0', color = '#F57C00']
    
    # TBL Performance Dimensions
    TBL_E [label = <<B>Economic</B><BR/><B>Performance</B><BR/><FONT POINT-SIZE='9'>(TBL-E)</FONT><BR/><FONT POINT-SIZE='8'>经济绩效</FONT>>, 
           fillcolor = '#E8F5E8', color = '#4CAF50']
    
    TBL_S [label = <<B>Social</B><BR/><B>Performance</B><BR/><FONT POINT-SIZE='9'>(TBL-S)</FONT><BR/><FONT POINT-SIZE='8'>社会绩效</FONT>>, 
           fillcolor = '#E8F5E8', color = '#4CAF50']
    
    TBL_EN [label = <<B>Environmental</B><BR/><B>Performance</B><BR/><FONT POINT-SIZE='9'>(TBL-EN)</FONT><BR/><FONT POINT-SIZE='8'>环境绩效</FONT>>, 
            fillcolor = '#E8F5E8', color = '#4CAF50']
    
    # Dependent Variable
    CA [label = <<B>Competitive</B><BR/><B>Advantage</B><BR/><FONT POINT-SIZE='9'>(CA)</FONT><BR/><FONT POINT-SIZE='8'>竞争优势</FONT>>, 
        fillcolor = '#FCE4EC', color = '#C2185B']
    
    # Moderators
    ED [label = <<B>Environmental</B><BR/><B>Dynamism</B><BR/><FONT POINT-SIZE='9'>(ED)</FONT><BR/><FONT POINT-SIZE='8'>环境动态性</FONT>>, 
        fillcolor = '#F3E5F5', color = '#7B1FA2', shape = ellipse]
    
    MCI [label = <<B>Market Competition</B><BR/><B>Intensity</B><BR/><FONT POINT-SIZE='9'>(MCI)</FONT><BR/><FONT POINT-SIZE='8'>市场竞争强度</FONT>>, 
         fillcolor = '#F3E5F5', color = '#7B1FA2', shape = ellipse]
    
    # ===== STRUCTURAL RELATIONSHIPS =====
    
    # H1 & H2: Direct antecedents to SIC
    DC -> SIC [label = <<FONT POINT-SIZE='10'><B>β₁</B></FONT><BR/><FONT POINT-SIZE='8'>H1</FONT>>, 
               color = '#1976D2', penwidth = 2.5, arrowsize = 1.2]
    
    RC -> SIC [label = <<FONT POINT-SIZE='10'><B>β₂</B></FONT><BR/><FONT POINT-SIZE='8'>H2</FONT>>, 
               color = '#388E3C', penwidth = 2.5, arrowsize = 1.2]
    
    # H3a-c: SIC to TBL dimensions
    SIC -> TBL_E [label = <<FONT POINT-SIZE='10'><B>β₃ₐ</B></FONT><BR/><FONT POINT-SIZE='8'>H3a</FONT>>, 
                  color = '#F57C00', penwidth = 2.5, arrowsize = 1.2]
    
    SIC -> TBL_S [label = <<FONT POINT-SIZE='10'><B>β₃ᵦ</B></FONT><BR/><FONT POINT-SIZE='8'>H3b</FONT>>, 
                  color = '#F57C00', penwidth = 2.5, arrowsize = 1.2]
    
    SIC -> TBL_EN [label = <<FONT POINT-SIZE='10'><B>β₃ᶜ</B></FONT><BR/><FONT POINT-SIZE='8'>H3c</FONT>>, 
                   color = '#F57C00', penwidth = 2.5, arrowsize = 1.2]
    
    # H4a-c: TBL dimensions to CA
    TBL_E -> CA [label = <<FONT POINT-SIZE='10'><B>β₄ₐ</B></FONT><BR/><FONT POINT-SIZE='8'>H4a</FONT>>, 
                 color = '#4CAF50', penwidth = 2.5, arrowsize = 1.2]
    
    TBL_S -> CA [label = <<FONT POINT-SIZE='10'><B>β₄ᵦ</B></FONT><BR/><FONT POINT-SIZE='8'>H4b</FONT>>, 
                 color = '#4CAF50', penwidth = 2.5, arrowsize = 1.2]
    
    TBL_EN -> CA [label = <<FONT POINT-SIZE='10'><B>β₄ᶜ</B></FONT><BR/><FONT POINT-SIZE='8'>H4c</FONT>>, 
                  color = '#4CAF50', penwidth = 2.5, arrowsize = 1.2]
    
    # H5a-b: Direct effects (dashed lines)
    DC -> TBL_E [label = <<FONT POINT-SIZE='9'>β₅ₐ₁</FONT><BR/><FONT POINT-SIZE='7'>H5a</FONT>>, 
                 color = '#FF5722', style = dashed, penwidth = 1.5]
    DC -> TBL_S [label = <<FONT POINT-SIZE='9'>β₅ₐ₂</FONT><BR/><FONT POINT-SIZE='7'>H5a</FONT>>, 
                 color = '#FF5722', style = dashed, penwidth = 1.5]
    DC -> TBL_EN [label = <<FONT POINT-SIZE='9'>β₅ₐ₃</FONT><BR/><FONT POINT-SIZE='7'>H5a</FONT>>, 
                  color = '#FF5722', style = dashed, penwidth = 1.5]
    
    RC -> TBL_E [label = <<FONT POINT-SIZE='9'>β₅ᵦ₁</FONT><BR/><FONT POINT-SIZE='7'>H5b</FONT>>, 
                 color = '#FF5722', style = dashed, penwidth = 1.5]
    RC -> TBL_S [label = <<FONT POINT-SIZE='9'>β₅ᵦ₂</FONT><BR/><FONT POINT-SIZE='7'>H5b</FONT>>, 
                 color = '#FF5722', style = dashed, penwidth = 1.5]
    RC -> TBL_EN [label = <<FONT POINT-SIZE='9'>β₅ᵦ₃</FONT><BR/><FONT POINT-SIZE='7'>H5b</FONT>>, 
                  color = '#FF5722', style = dashed, penwidth = 1.5]
    
    # H7: Moderation effects (dotted lines)
    ED -> SIC [label = <<FONT POINT-SIZE='8'>Moderates</FONT><BR/><FONT POINT-SIZE='7'>DC/RC→SIC</FONT><BR/><FONT POINT-SIZE='7'>H7</FONT>>, 
               color = '#7B1FA2', style = dotted, penwidth = 2, arrowhead = odot]
    
    MCI -> CA [label = <<FONT POINT-SIZE='8'>Moderates</FONT><BR/><FONT POINT-SIZE='7'>TBL→CA</FONT><BR/><FONT POINT-SIZE='7'>H7</FONT>>, 
               color = '#7B1FA2', style = dotted, penwidth = 2, arrowhead = odot]
    
    # Covariances (bidirectional dotted gray lines)
    DC -> RC [dir = both, label = '', color = '#9E9E9E', style = dotted, penwidth = 1]
    TBL_E -> TBL_S [dir = both, label = '', color = '#9E9E9E', style = dotted, penwidth = 1]
    TBL_E -> TBL_EN [dir = both, label = '', color = '#9E9E9E', style = dotted, penwidth = 1]
    TBL_S -> TBL_EN [dir = both, label = '', color = '#9E9E9E', style = dotted, penwidth = 1]
    
    # ===== TITLE AND SUBTITLE =====
    labelloc = 't'
    label = <<FONT POINT-SIZE='16'><B>Structural Equation Model: Dynamic Capabilities, Service Innovation, and Triple Bottom Line Performance</B></FONT><BR/><FONT POINT-SIZE='12'>Sequential Mediation Framework with Moderation Effects</FONT>>
  }
  ")
  
  return(diagram)
}

# ===============================================================================
# CREATE LEGEND AS SEPARATE DIAGRAM
# ===============================================================================

create_legend_diagram <- function() {
  
  legend <- grViz("
  digraph legend {
    
    graph [layout = dot, rankdir = TB, bgcolor = white, fontname = 'Times New Roman']
    
    node [shape = plaintext, fontname = 'Times New Roman', fontsize = 11]
    
    # Legend title
    title [label = <<FONT POINT-SIZE='14'><B>Legend</B></FONT>>]
    
    # Path types
    direct [label = <<FONT POINT-SIZE='11'><B>Path Types:</B></FONT>>]
    p1 [label = <<FONT COLOR='#1976D2'>━━━━━</FONT> Direct Antecedent Effects (H1, H2)>]
    p2 [label = <<FONT COLOR='#F57C00'>━━━━━</FONT> Mediation through SIC (H3a-c)>]
    p3 [label = <<FONT COLOR='#4CAF50'>━━━━━</FONT> TBL to Competitive Advantage (H4a-c)>]
    p4 [label = <<FONT COLOR='#FF5722'>┅┅┅┅┅</FONT> Direct Effects (H5a-b)>]
    p5 [label = <<FONT COLOR='#7B1FA2'>⋯⋯⋯⋯⋯</FONT> Moderation Effects (H7)>]
    p6 [label = <<FONT COLOR='#9E9E9E'>⋯⋯⋯⋯⋯</FONT> Covariances>]
    
    # Variable types
    vars [label = <<FONT POINT-SIZE='11'><B>Variable Types:</B></FONT>>]
    v1 [label = <<FONT COLOR='#1976D2'>■</FONT> Independent Variables>]
    v2 [label = <<FONT COLOR='#F57C00'>■</FONT> Mediator Variable>]
    v3 [label = <<FONT COLOR='#4CAF50'>■</FONT> TBL Performance Dimensions>]
    v4 [label = <<FONT COLOR='#C2185B'>■</FONT> Dependent Variable>]
    v5 [label = <<FONT COLOR='#7B1FA2'>●</FONT> Moderator Variables>]
    
    # Layout
    title -> direct -> p1 -> p2 -> p3 -> p4 -> p5 -> p6 -> vars -> v1 -> v2 -> v3 -> v4 -> v5
  }
  ")
  
  return(legend)
}

# ===============================================================================
# GENERATE COMPLETE VISUALIZATION PACKAGE
# ===============================================================================

generate_publication_ready_diagrams <- function() {
  
  cat("Generating publication-ready SEM diagrams...\n\n")
  
  # Create main diagram
  main_diagram <- create_advanced_sem_diagram()
  
  # Create legend
  legend_diagram <- create_legend_diagram()
  
  # Display diagrams
  print(main_diagram)
  print(legend_diagram)
  
  # Save as high-resolution images
  if(require(webshot, quietly = TRUE)) {

    # Save main diagram as HTML first, then convert to PNG
    temp_html_main <- tempfile(fileext = ".html")
    temp_html_legend <- tempfile(fileext = ".html")

    # Save HTML files
    htmlwidgets::saveWidget(main_diagram, temp_html_main, selfcontained = TRUE)
    htmlwidgets::saveWidget(legend_diagram, temp_html_legend, selfcontained = TRUE)

    # Convert to PNG
    webshot::webshot(temp_html_main, "Advanced_SEM_Framework.png",
                     vwidth = 1400, vheight = 900, zoom = 2)
    webshot::webshot(temp_html_legend, "SEM_Legend.png",
                     vwidth = 400, vheight = 600, zoom = 2)

    # Clean up temp files
    unlink(temp_html_main)
    unlink(temp_html_legend)

    cat("High-resolution diagrams saved:\n")
    cat("- Advanced_SEM_Framework.png (1400x900)\n")
    cat("- SEM_Legend.png (400x600)\n\n")
  } else {
    cat("webshot package not available. Diagrams displayed but not saved as PNG.\n")
  }
  
  # Return both diagrams
  return(list(main = main_diagram, legend = legend_diagram))
}

# ===============================================================================
# EXECUTE ADVANCED VISUALIZATION
# ===============================================================================

# Generate the complete visualization package
diagrams <- generate_publication_ready_diagrams()

cat("=== ADVANCED SEM VISUALIZATION COMPLETE ===\n")
cat("Features included:\n")
cat("• Publication-quality typography (Times New Roman)\n")
cat("• High-resolution output (300 DPI)\n") 
cat("• Color-coded path types and variable categories\n")
cat("• Bilingual labels (English/Chinese)\n")
cat("• Professional layout with proper spacing\n")
cat("• Comprehensive legend\n")
cat("• Sequential mediation pathway clearly shown\n")
cat("• Moderation effects distinctly marked\n\n")

cat("Ready for academic publication!\n")
